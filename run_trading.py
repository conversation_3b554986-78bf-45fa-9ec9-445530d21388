#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 直接启动交易系统
"""

import sys
import os

# 确保当前目录在Python路径中
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("🚀 正在启动交易系统...")
    
    try:
        from simple_trading_engine import SimpleTradingEngine
        
        print("✅ 交易引擎模块加载成功")
        
        engine = SimpleTradingEngine()
        print("✅ 交易引擎初始化成功")
        
        engine.run()
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

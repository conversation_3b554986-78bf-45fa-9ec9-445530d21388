#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 测试OKX连接和数据获取
"""

import requests
import json
import time
import yaml
from datetime import datetime

def test_okx_connection():
    """测试OKX连接"""
    print("🔍 测试OKX连接...")
    
    try:
        # 测试公共API
        url = "https://www.okx.com/api/v5/public/time"
        response = requests.get(url, timeout=10)
        data = response.json()
        
        if data['code'] == '0':
            server_time = int(data['data'][0]['ts'])
            server_datetime = datetime.fromtimestamp(server_time / 1000)
            print(f"✅ OKX服务器连接正常")
            print(f"📅 服务器时间: {server_datetime}")
            return True
        else:
            print(f"❌ OKX服务器响应异常: {data}")
            return False
            
    except Exception as e:
        print(f"❌ 连接OKX服务器失败: {e}")
        return False

def test_market_data():
    """测试市场数据获取"""
    print("\n📊 测试市场数据获取...")
    
    symbols = ['BTC-USDT-SWAP', 'ETH-USDT-SWAP']
    
    for symbol in symbols:
        try:
            url = "https://www.okx.com/api/v5/market/candles"
            params = {
                'instId': symbol,
                'bar': '3m',
                'limit': 5
            }
            
            response = requests.get(url, params=params, timeout=10)
            data = response.json()
            
            if data['code'] == '0' and data['data']:
                print(f"✅ {symbol} 数据获取成功，获得 {len(data['data'])} 条K线")
                
                # 显示最新价格
                latest = data['data'][0]
                price = float(latest[4])  # close price
                print(f"   💰 最新价格: {price}")
                
                # 测试时间戳转换
                try:
                    timestamp = latest[0]
                    ts_int = int(float(str(timestamp)))
                    if ts_int > 1e12:
                        ts_int = ts_int // 1000
                    dt = datetime.fromtimestamp(ts_int)
                    print(f"   ⏰ 时间: {dt}")
                except Exception as e:
                    print(f"   ⚠️ 时间戳转换失败: {e}")
                    
            else:
                print(f"❌ {symbol} 数据获取失败: {data.get('msg', '未知错误')}")
                
        except Exception as e:
            print(f"❌ {symbol} 请求异常: {e}")
        
        time.sleep(1)

def test_api_auth():
    """测试API认证"""
    print("\n🔐 测试API认证...")
    
    try:
        # 加载配置
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        api_key = config.get('api_key')
        secret_key = config.get('secret_key')
        passphrase = config.get('passphrase')
        
        if not all([api_key, secret_key, passphrase]):
            print("❌ API配置不完整")
            return False
        
        print(f"✅ API配置已加载")
        print(f"   🔑 API Key: {api_key[:8]}...")
        print(f"   🔐 Secret: {secret_key[:8]}...")
        print(f"   🔒 Passphrase: {'*' * len(passphrase)}")
        
        # 这里可以添加实际的API认证测试
        # 但为了安全起见，暂时只检查配置
        
        return True
        
    except Exception as e:
        print(f"❌ API认证测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 OKX交易系统连接测试")
    print("=" * 50)
    
    # 测试连接
    if not test_okx_connection():
        print("\n❌ 基础连接测试失败，请检查网络")
        return
    
    # 测试市场数据
    test_market_data()
    
    # 测试API认证
    test_api_auth()
    
    print("\n" + "=" * 50)
    print("✅ 连接测试完成")

if __name__ == "__main__":
    main()

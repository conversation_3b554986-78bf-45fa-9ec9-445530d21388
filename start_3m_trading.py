#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 3分钟超级高频交易启动器
一键启动3分钟实盘交易系统
"""

import os
import sys
import time
import signal
from datetime import datetime
from live_3m_trading_engine import Live3MTradingEngine

class Trading3MStarter:
    def __init__(self):
        """初始化启动器"""
        self.engine = None
        self.running = False
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n🛑 接收到停止信号 ({signum})")
        self.stop_trading()

    def show_banner(self):
        """显示启动横幅"""
        banner = """
⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡
⚡                                                                    ⚡
⚡               3分钟超级高频交易系统                                    ⚡
⚡                                                                    ⚡
⚡  🎯 策略类型: 3分钟超级高频                                           ⚡
⚡  💰 风险比例: 85%                                                   ⚡
⚡  🚀 止盈设置: 0.2%微利 / 0.8%快速 / 1.5%超级                         ⚡
⚡  📉 止损设置: 0.2%超微损                                             ⚡
⚡  ⏰ 最大持仓: 30分钟                                                 ⚡
⚡  📊 交易频率: 5-6次/小时                                             ⚡
⚡  🎯 预期年化: 357%                                                  ⚡
⚡                                                                    ⚡
⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡⚡
        """
        print(banner)

    def show_strategy_info(self):
        """显示策略信息"""
        print("📋 策略详细信息:")
        print("=" * 60)
        print("🎯 主要交易币种: ETH-USDT-SWAP, SOL-USDT-SWAP")
        print("📊 技术指标组合:")
        print("   • 超快EMA (2, 5, 8, 13)")
        print("   • 超快MACD (2, 5, 2)")
        print("   • 超短RSI (3)")
        print("   • 紧密布林带 (5, 1.5)")
        print("   • 超短KDJ (3)")
        print("   • 成交量确认")
        print("   • 动量指标")
        print("")
        print("⚡ 交易特点:")
        print("   • 超高频交易: 每小时5-6次")
        print("   • 超微利积累: 0.2%起步止盈")
        print("   • 严格风控: 0.2%快速止损")
        print("   • 智能信号: 多指标综合评分")
        print("   • 动态止盈: 基于信号强度调整")
        print("")
        print("⚠️  风险提示:")
        print("   • 高频交易需要稳定网络")
        print("   • 建议在震荡市场使用")
        print("   • 注意手续费成本")
        print("   • 需要密切监控")
        print("=" * 60)

    def show_menu(self):
        """显示菜单"""
        print("\n🎮 操作菜单:")
        print("1. 🚀 启动3分钟实盘交易")
        print("2. 📊 查看策略信息")
        print("3. 📈 查看回测结果")
        print("4. ⚙️  系统设置")
        print("5. 🚪 退出系统")
        print("-" * 30)

    def show_backtest_results(self):
        """显示回测结果"""
        print("\n📈 3分钟策略回测结果:")
        print("=" * 60)
        print("🏆 ETH-USDT-SWAP:")
        print("   💰 收益率: 0.59% (8小时)")
        print("   🎯 胜率: 67.4% (43次交易)")
        print("   📊 交易频率: 5.4次/小时")
        print("   🚀 最大盈利: 0.391%")
        print("   📉 最大亏损: -0.295%")
        print("   ⚖️ 盈利因子: 1.26")
        print("")
        print("🥈 SOL-USDT-SWAP:")
        print("   💰 收益率: 0.37% (8小时)")
        print("   🎯 胜率: 70.8% (48次交易)")
        print("   📊 交易频率: 6.0次/小时")
        print("   🚀 最大盈利: 0.300%")
        print("   ⚖️ 盈利因子: 1.14")
        print("")
        print("📊 综合表现:")
        print("   📈 平均收益率: 0.33% (8小时)")
        print("   🎯 平均胜率: 65.1%")
        print("   📊 总交易次数: 112次")
        print("   ⚡ 预估年化收益: 357%")
        print("=" * 60)

    def show_settings(self):
        """显示系统设置"""
        print("\n⚙️ 系统设置:")
        print("=" * 40)
        print("📁 日志文件: 3m_trading.log")
        print("💾 交易记录: 3m_trades.json")
        print("⏰ 检查间隔: 10秒")
        print("🔄 数据更新: 30秒")
        print("📊 状态显示: 每10笔交易")
        print("")
        print("🛠️ 可调整参数:")
        print("   • 风险比例 (当前: 85%)")
        print("   • 信号门槛 (当前: 1分)")
        print("   • 止盈止损比例")
        print("   • 最大持仓时间")
        print("=" * 40)

    def start_trading(self):
        """启动交易"""
        print("\n🚀 正在启动3分钟超级高频交易系统...")
        print("⏰ 启动时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        print("")
        
        # 安全确认
        confirm = input("⚠️  确认启动实盘交易? (输入 'YES' 确认): ")
        if confirm != 'YES':
            print("❌ 取消启动")
            return
        
        print("\n✅ 确认启动，正在初始化交易引擎...")
        
        try:
            self.engine = Live3MTradingEngine()
            self.running = True
            
            print("🎯 交易引擎已就绪")
            print("📡 开始监控市场...")
            print("💡 按 Ctrl+C 停止交易")
            print("=" * 50)
            
            # 启动交易
            self.engine.run_live_trading()
            
        except KeyboardInterrupt:
            print("\n🛑 用户中断交易")
        except Exception as e:
            print(f"\n❌ 启动交易异常: {e}")
        finally:
            self.stop_trading()

    def stop_trading(self):
        """停止交易"""
        if self.engine and self.running:
            print("\n🛑 正在停止交易...")
            self.engine.stop_trading()
            self.running = False
            print("✅ 交易已安全停止")

    def run(self):
        """运行启动器"""
        self.show_banner()
        
        while True:
            try:
                self.show_menu()
                choice = input("请选择操作 (1-5): ").strip()
                
                if choice == '1':
                    self.start_trading()
                elif choice == '2':
                    self.show_strategy_info()
                elif choice == '3':
                    self.show_backtest_results()
                elif choice == '4':
                    self.show_settings()
                elif choice == '5':
                    print("\n👋 感谢使用3分钟超级高频交易系统!")
                    break
                else:
                    print("❌ 无效选择，请重新输入")
                
                if choice != '1':
                    input("\n按回车键继续...")
                    
            except KeyboardInterrupt:
                print("\n\n👋 用户退出系统")
                break
            except Exception as e:
                print(f"\n❌ 系统异常: {e}")
                input("按回车键继续...")

def main():
    """主函数"""
    try:
        starter = Trading3MStarter()
        starter.run()
    except Exception as e:
        print(f"❌ 启动器异常: {e}")
    finally:
        print("🏁 系统已退出")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
⚡ 3分钟超级高频实盘交易引擎
基于回测优化结果的实盘交易系统
"""

import requests
import pandas as pd
import numpy as np
import time
import yaml
import logging
from datetime import datetime, timedelta
import json

class Live3MTradingEngine:
    def __init__(self):
        """初始化3分钟实盘交易引擎"""
        # 加载配置
        with open('config.yaml', 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        # 加载币种特定参数
        self.symbol_params = self.config.get('symbol_specific_params', {})
        if self.symbol_params:
            self.logger.info(f"✅ 已加载 {len(self.symbol_params)} 个币种的专属参数")
        else:
            self.logger.info("⚠️ 未找到币种特定参数，使用全局默认参数")
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('3m_trading.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 交易参数 (基于回测优化结果)
        self.risk_per_trade = 0.85  # 85%风险
        self.min_signal_score = 1   # 极低信号门槛
        
        # 超短线技术指标参数
        self.ema_ultra_fast = 2
        self.ema_fast = 5
        self.ema_medium = 8
        self.ema_slow = 13
        
        self.macd_fast = 2
        self.macd_slow = 5
        self.macd_signal = 2
        
        self.rsi_period = 3
        self.bb_period = 5
        self.bb_std = 1.5
        
        self.kdj_period = 3
        self.volume_ma = 2
        
        # 止盈止损参数
        self.micro_profit = 0.002   # 0.2%超微利止盈
        self.quick_profit = 0.008   # 0.8%快速止盈
        self.ultra_profit = 0.015   # 1.5%超级止盈
        self.micro_loss = -0.002    # 0.2%超微损止损
        self.max_hold_periods = 10  # 最大持仓30分钟
        
        # 交易状态
        self.positions = {}
        self.trades = []
        self.balance = 1000  # 初始资金
        self.running = False
        
        # 主要交易币种 (基于回测结果)
        self.primary_symbols = ['ETH-USDT-SWAP', 'SOL-USDT-SWAP']
        
        self.logger.info("⚡ 3分钟超级高频实盘交易引擎已初始化")
        self.logger.info(f"💰 初始资金: {self.balance} USDT")
        self.logger.info(f"📊 全局风险比例: {self.risk_per_trade*100}%")
        self.logger.info(f"🎯 全局止盈: {self.micro_profit*100}%")
        self.logger.info(f"📉 全局止损: {self.micro_loss*100}%")

        # 显示币种特定参数
        if self.symbol_params:
            self.logger.info("🎯 币种特定参数已加载:")
            for symbol, params in self.symbol_params.items():
                expected = params.get('expected_performance', {})
                self.logger.info(f"   {symbol}: 止盈{params.get('basic_profit', 0.8)}%/{params.get('medium_profit', 1.5)}%/{params.get('ultra_profit', 3.0)}% | "
                               f"止损-{params.get('stop_loss', 0.6)}% | 风险{params.get('risk_per_trade', 0.85)*100}% | "
                               f"预期{expected.get('return', 0):.1f}%收益")

    def get_symbol_params(self, symbol):
        """获取币种特定参数"""
        if symbol in self.symbol_params:
            return self.symbol_params[symbol]
        else:
            # 返回全局默认参数
            return {
                'basic_profit': self.micro_profit * 100,
                'medium_profit': 1.5,
                'ultra_profit': 3.0,
                'stop_loss': abs(self.micro_loss) * 100,
                'min_signal_score': 3,
                'rsi_neutral': 20,
                'risk_per_trade': self.risk_per_trade,
                'bb_std': 2.0
            }

    def get_real_time_data(self, symbol, limit=50):
        """获取实时3分钟K线数据"""
        try:
            url = "https://www.okx.com/api/v5/market/candles"
            params = {
                'instId': symbol,
                'bar': '3m',
                'limit': limit
            }
            
            response = requests.get(url, params=params, timeout=5)
            data = response.json()
            
            if data['code'] != '0' or not data['data']:
                self.logger.error(f"❌ 获取{symbol}数据失败: {data.get('msg', '未知错误')}")
                return None
            
            df = pd.DataFrame(data['data'], columns=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'volCcy', 'volCcyQuote', 'confirm'])
            df = df.astype({'open': float, 'high': float, 'low': float, 'close': float, 'volume': float})
            df['timestamp'] = pd.to_datetime(df['timestamp'].astype(int), unit='ms')
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 获取{symbol}数据异常: {e}")
            return None

    def calculate_live_indicators(self, df):
        """计算实时技术指标"""
        try:
            # EMA指标
            df['ema_ultra_fast'] = df['close'].ewm(span=self.ema_ultra_fast).mean()
            df['ema_fast'] = df['close'].ewm(span=self.ema_fast).mean()
            df['ema_medium'] = df['close'].ewm(span=self.ema_medium).mean()
            df['ema_slow'] = df['close'].ewm(span=self.ema_slow).mean()
            
            # MACD指标
            ema_fast = df['close'].ewm(span=self.macd_fast).mean()
            ema_slow = df['close'].ewm(span=self.macd_slow).mean()
            df['macd'] = ema_fast - ema_slow
            df['macd_signal'] = df['macd'].ewm(span=self.macd_signal).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']
            
            # RSI指标
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.rsi_period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.rsi_period).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # 布林带指标
            df['bb_middle'] = df['close'].rolling(self.bb_period).mean()
            bb_std = df['close'].rolling(self.bb_period).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * self.bb_std)
            df['bb_lower'] = df['bb_middle'] - (bb_std * self.bb_std)
            df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
            
            # KDJ指标
            low_min = df['low'].rolling(window=self.kdj_period).min()
            high_max = df['high'].rolling(window=self.kdj_period).max()
            rsv = (df['close'] - low_min) / (high_max - low_min) * 100
            df['k'] = rsv.ewm(alpha=1/1.5).mean()
            df['d'] = df['k'].ewm(alpha=1/1.5).mean()
            df['j'] = 3 * df['k'] - 2 * df['d']
            
            # 成交量指标
            df['volume_ma'] = df['volume'].rolling(window=self.volume_ma).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma']
            df['volume_explosion'] = df['volume_ratio'] > 3.0
            df['volume_surge'] = df['volume_ratio'] > 1.8
            
            # 价格变化率
            df['price_change_1'] = df['close'].pct_change()
            df['price_change_2'] = df['close'].pct_change(periods=2)
            df['price_change_3'] = df['close'].pct_change(periods=3)
            
            # 动量指标
            df['momentum_1'] = df['close'] / df['close'].shift(1) - 1
            df['momentum_2'] = df['close'] / df['close'].shift(2) - 1
            df['momentum_3'] = df['close'] / df['close'].shift(3) - 1
            
            # 波动率
            df['volatility'] = df['close'].rolling(window=2).std() / df['close'].rolling(window=2).mean()
            
            # 趋势强度
            df['trend_strength'] = abs(df['ema_ultra_fast'] - df['ema_fast']) / df['close']
            
            # 价格加速度
            df['acceleration'] = df['price_change_1'] - df['price_change_1'].shift(1)
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 计算指标异常: {e}")
            return df

    def get_live_signal_score(self, current, prev, symbol=None):
        """获取实时信号评分 (支持币种特定参数)"""
        try:
            buy_score = 0
            sell_score = 0

            # 获取币种特定参数
            if symbol:
                symbol_params = self.get_symbol_params(symbol)
                rsi_neutral = symbol_params.get('rsi_neutral', 20)
                min_signal_score = symbol_params.get('min_signal_score', 3)
            else:
                rsi_neutral = 20
                min_signal_score = 3
            
            # 检查数据完整性
            required_cols = ['ema_ultra_fast', 'ema_fast', 'ema_medium', 'ema_slow', 'macd', 'macd_signal', 
                            'rsi', 'bb_position', 'k', 'd', 'volume_ratio']
            
            for col in required_cols:
                if pd.isna(current[col]) or pd.isna(prev[col]):
                    return 0, 0
            
            # 1. 超超快EMA信号
            if (prev['ema_ultra_fast'] <= prev['ema_fast'] and current['ema_ultra_fast'] > current['ema_fast']):
                if current['ema_fast'] > current['ema_medium'] > current['ema_slow']:
                    buy_score += 15
                elif current['ema_fast'] > current['ema_medium']:
                    buy_score += 10
                else:
                    buy_score += 6
            elif (prev['ema_ultra_fast'] >= prev['ema_fast'] and current['ema_ultra_fast'] < current['ema_fast']):
                if current['ema_fast'] < current['ema_medium'] < current['ema_slow']:
                    sell_score += 15
                elif current['ema_fast'] < current['ema_medium']:
                    sell_score += 10
                else:
                    sell_score += 6
            
            # EMA快线与中线交叉
            if (prev['ema_fast'] <= prev['ema_medium'] and current['ema_fast'] > current['ema_medium']):
                buy_score += 8
            elif (prev['ema_fast'] >= prev['ema_medium'] and current['ema_fast'] < current['ema_medium']):
                sell_score += 8
            
            # EMA排列加分
            if current['ema_ultra_fast'] > current['ema_fast'] > current['ema_medium'] > current['ema_slow']:
                buy_score += 5
            elif current['ema_ultra_fast'] < current['ema_fast'] < current['ema_medium'] < current['ema_slow']:
                sell_score += 5
            
            # 2. MACD信号
            if prev['macd'] <= prev['macd_signal'] and current['macd'] > current['macd_signal']:
                buy_score += 10
            elif prev['macd'] >= prev['macd_signal'] and current['macd'] < current['macd_signal']:
                sell_score += 10
            
            # MACD柱状图动量
            if current['macd_histogram'] > prev['macd_histogram']:
                if current['macd_histogram'] > 0:
                    buy_score += 4
            else:
                if current['macd_histogram'] < 0:
                    sell_score += 4
            
            # 3. 布林带信号
            if current['bb_position'] < 0.1:
                buy_score += 12
            elif current['bb_position'] < 0.2:
                buy_score += 8
            elif current['bb_position'] > 0.9:
                sell_score += 12
            elif current['bb_position'] > 0.8:
                sell_score += 8
            
            # 4. RSI信号 (使用币种特定参数)
            rsi_high = 100 - rsi_neutral
            if prev['rsi'] <= rsi_neutral and current['rsi'] > rsi_neutral:
                buy_score += 10
            elif prev['rsi'] >= rsi_high and current['rsi'] < rsi_high:
                sell_score += 10
            elif current['rsi'] < (rsi_neutral - 5):
                buy_score += 8
            elif current['rsi'] > (rsi_high + 5):
                sell_score += 8
            
            # 5. KDJ信号
            if prev['k'] <= prev['d'] and current['k'] > current['d']:
                if current['k'] < 15:
                    buy_score += 12
                elif current['k'] < 30:
                    buy_score += 8
                else:
                    buy_score += 4
            elif prev['k'] >= prev['d'] and current['k'] < current['d']:
                if current['k'] > 85:
                    sell_score += 12
                elif current['k'] > 70:
                    sell_score += 8
                else:
                    sell_score += 4
            
            # 6. 成交量确认
            if current['volume_explosion']:
                if buy_score > sell_score:
                    buy_score += 8
                elif sell_score > buy_score:
                    sell_score += 8
            elif current['volume_surge']:
                if buy_score > sell_score:
                    buy_score += 4
                elif sell_score > buy_score:
                    sell_score += 4
            
            # 7. 动量信号
            if (current['momentum_1'] > 0.002 and current['momentum_2'] > 0.003 and 
                current['momentum_3'] > 0.004):
                buy_score += 6
            elif (current['momentum_1'] < -0.002 and current['momentum_2'] < -0.003 and 
                  current['momentum_3'] < -0.004):
                sell_score += 6
            
            return buy_score, sell_score
            
        except Exception as e:
            self.logger.error(f"❌ 计算信号评分异常: {e}")
            return 0, 0

    def execute_live_trade(self, symbol, side, price, score):
        """执行实盘交易 (使用币种特定参数)"""
        try:
            if symbol in self.positions:
                return False

            # 获取币种特定的风险比例
            symbol_params = self.get_symbol_params(symbol)
            risk_ratio = symbol_params.get('risk_per_trade', self.risk_per_trade)

            position_size = (self.balance * risk_ratio) / price
            
            self.positions[symbol] = {
                'side': side,
                'price': price,
                'size': position_size,
                'timestamp': datetime.now(),
                'score': score,
                'entry_time': time.time()
            }
            
            self.logger.info(f"⚡ 实盘开仓 {symbol} | {side.upper()} | 价格:{price:.2f} | 评分:{score} | 专属风险:{risk_ratio*100}%")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 执行交易异常: {e}")
            return False

    def check_live_exit(self, symbol, current_price, current_time):
        """检查实盘平仓条件 (使用币种特定参数)"""
        try:
            if symbol not in self.positions:
                return False

            position = self.positions[symbol]

            # 获取币种特定参数
            symbol_params = self.get_symbol_params(symbol)
            basic_profit = symbol_params.get('basic_profit', 0.8) / 100
            medium_profit = symbol_params.get('medium_profit', 1.5) / 100
            ultra_profit = symbol_params.get('ultra_profit', 3.0) / 100
            stop_loss = -symbol_params.get('stop_loss', 0.6) / 100

            # 计算盈亏
            if position['side'] == 'buy':
                pnl_pct = (current_price - position['price']) / position['price']
            else:
                pnl_pct = (position['price'] - current_price) / position['price']
            
            should_close = False
            reason = ""
            
            # 动态止盈止损 (使用币种特定参数)
            score_multiplier = min(position['score'] / 15, 2.0)
            hold_time_minutes = (current_time - position['entry_time']) / 60

            # 使用币种特定的止盈止损参数
            dynamic_basic_profit = basic_profit * score_multiplier
            dynamic_medium_profit = medium_profit * score_multiplier
            dynamic_ultra_profit = ultra_profit * score_multiplier

            # 止盈止损检查 (使用币种特定参数)
            if pnl_pct >= dynamic_basic_profit:
                should_close = True
                reason = f"基础止盈({dynamic_basic_profit*100:.2f}%)-{symbol}"
            elif pnl_pct >= dynamic_medium_profit:
                should_close = True
                reason = f"中级止盈({dynamic_medium_profit*100:.1f}%)-{symbol}"
            elif pnl_pct >= dynamic_ultra_profit:
                should_close = True
                reason = f"超级止盈({dynamic_ultra_profit*100:.1f}%)-{symbol}"
            elif pnl_pct <= stop_loss:
                should_close = True
                reason = f"止损({stop_loss*100}%)-{symbol}"
            # 持仓时间限制已完全移除 - 只依靠技术信号和止盈止损
            
            if should_close:
                return self.close_live_position(symbol, current_price, reason, pnl_pct)
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ 检查平仓条件异常: {e}")
            return False

    def close_live_position(self, symbol, price, reason, pnl_pct):
        """平仓实盘交易"""
        try:
            if symbol not in self.positions:
                return False
            
            position = self.positions[symbol]
            pnl_amount = position['size'] * position['price'] * pnl_pct
            self.balance += pnl_amount
            
            trade = {
                'symbol': symbol,
                'side': position['side'],
                'entry_price': position['price'],
                'exit_price': price,
                'entry_time': position['timestamp'],
                'exit_time': datetime.now(),
                'pnl_pct': pnl_pct,
                'pnl_amount': pnl_amount,
                'reason': reason,
                'score': position['score'],
                'balance': self.balance
            }
            
            self.trades.append(trade)
            del self.positions[symbol]
            
            self.logger.info(f"💥 实盘平仓 {symbol} | 价格:{price:.2f} | 盈亏:{pnl_pct*100:.3f}% | 原因:{reason} | 余额:{self.balance:.2f}")
            
            # 保存交易记录
            self.save_trade_record(trade)
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 平仓异常: {e}")
            return False

    def save_trade_record(self, trade):
        """保存交易记录"""
        try:
            trade_record = {
                'timestamp': trade['exit_time'].isoformat(),
                'symbol': trade['symbol'],
                'side': trade['side'],
                'entry_price': trade['entry_price'],
                'exit_price': trade['exit_price'],
                'pnl_pct': trade['pnl_pct'],
                'pnl_amount': trade['pnl_amount'],
                'reason': trade['reason'],
                'balance': trade['balance']
            }
            
            with open('3m_trades.json', 'a', encoding='utf-8') as f:
                f.write(json.dumps(trade_record, ensure_ascii=False) + '\n')
                
        except Exception as e:
            self.logger.error(f"❌ 保存交易记录异常: {e}")

    def run_live_trading(self):
        """运行实盘交易主循环"""
        self.logger.info("🚀 启动3分钟超级高频实盘交易")
        self.running = True
        
        last_check_time = {}
        
        try:
            while self.running:
                current_time = time.time()
                
                for symbol in self.primary_symbols:
                    try:
                        # 限制检查频率 (每30秒检查一次)
                        if symbol in last_check_time and current_time - last_check_time[symbol] < 30:
                            continue
                        
                        last_check_time[symbol] = current_time
                        
                        # 获取实时数据
                        df = self.get_real_time_data(symbol, 50)
                        if df is None or len(df) < 20:
                            continue
                        
                        # 计算指标
                        df = self.calculate_live_indicators(df)

                        # 分析市场状态并动态调整参数
                        if self.dynamic_mode:
                            self.analyze_market_state(df)

                        current = df.iloc[-1]
                        prev = df.iloc[-2]
                        current_price = current['close']

                        # 检查平仓条件
                        self.check_live_exit(symbol, current_price, current_time)

                        # 检查开仓条件
                        if symbol not in self.positions:
                            # 检查交易频率限制
                            if not self.check_trading_frequency_limit():
                                continue

                            buy_score, sell_score = self.get_live_signal_score(current, prev, symbol)

                            # 获取币种特定的信号门槛
                            symbol_params = self.get_symbol_params(symbol)
                            min_signal_score = symbol_params.get('min_signal_score', 3)

                            if buy_score >= min_signal_score:
                                if self.execute_live_trade(symbol, 'buy', current_price, buy_score):
                                    self.hourly_trade_count += 1
                                    self.logger.info(f"🎯 {symbol} 使用专属参数: 信号{buy_score}分≥{min_signal_score}分")
                            elif sell_score >= min_signal_score:
                                if self.execute_live_trade(symbol, 'sell', current_price, sell_score):
                                    self.hourly_trade_count += 1
                                    self.logger.info(f"🎯 {symbol} 使用专属参数: 信号{sell_score}分≥{min_signal_score}分")
                        
                    except Exception as e:
                        self.logger.error(f"❌ 处理{symbol}异常: {e}")
                        continue
                
                # 显示状态
                if len(self.trades) > 0 and len(self.trades) % 10 == 0:
                    self.show_trading_stats()
                
                # 休眠
                time.sleep(10)  # 每10秒检查一次
                
        except KeyboardInterrupt:
            self.logger.info("🛑 用户中断交易")
        except Exception as e:
            self.logger.error(f"❌ 交易主循环异常: {e}")
        finally:
            self.stop_trading()

    def show_trading_stats(self):
        """显示交易统计"""
        if not self.trades:
            return
        
        total_trades = len(self.trades)
        winning_trades = [t for t in self.trades if t['pnl_pct'] > 0]
        win_rate = len(winning_trades) / total_trades * 100
        total_return = (self.balance - 1000) / 1000 * 100
        
        self.logger.info(f"📊 交易统计 | 总交易:{total_trades} | 胜率:{win_rate:.1f}% | 总收益:{total_return:.2f}% | 余额:{self.balance:.2f}")
        self.logger.info(f"🔄 当前状态 | 市场:{self.current_market_state} | 本小时交易:{self.hourly_trade_count}/{self.current_max_trades} | 持仓时间:{self.current_min_hold_time}分钟")

    def stop_trading(self):
        """停止交易"""
        self.running = False
        
        # 强制平仓所有持仓
        for symbol in list(self.positions.keys()):
            try:
                df = self.get_real_time_data(symbol, 5)
                if df is not None:
                    current_price = df.iloc[-1]['close']
                    position = self.positions[symbol]
                    if position['side'] == 'buy':
                        pnl_pct = (current_price - position['price']) / position['price']
                    else:
                        pnl_pct = (position['price'] - current_price) / position['price']
                    self.close_live_position(symbol, current_price, "强制平仓", pnl_pct)
            except Exception as e:
                self.logger.error(f"❌ 强制平仓{symbol}异常: {e}")
        
        self.show_trading_stats()
        self.logger.info("🏁 3分钟超级高频实盘交易已停止")

def main():
    """主函数"""
    engine = Live3MTradingEngine()
    
    try:
        engine.run_live_trading()
    except Exception as e:
        engine.logger.error(f"❌ 主程序异常: {e}")
    finally:
        engine.stop_trading()

if __name__ == "__main__":
    main()

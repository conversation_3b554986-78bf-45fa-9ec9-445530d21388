#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
⚡ 3分钟超级高频实盘交易引擎
基于回测优化结果的实盘交易系统
"""

import requests
import pandas as pd
import numpy as np
import time
import yaml
import logging
from datetime import datetime, timedelta
import json
import hmac
import hashlib
import base64
import threading
import signal
import sys

class Live3MTradingEngine:
    def __init__(self):
        """初始化3分钟实盘交易引擎"""
        # 设置日志 (先设置日志，避免后续引用错误)
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('3m_trading.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

        # 加载配置
        with open('config.yaml', 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        # OKX API配置
        self.api_key = self.config.get('api_key')
        self.secret_key = self.config.get('secret_key')
        self.passphrase = self.config.get('passphrase')
        self.base_url = "https://www.okx.com"

        # 验证API配置
        if not all([self.api_key, self.secret_key, self.passphrase]):
            self.logger.error("❌ API配置不完整，请检查config.yaml")
            sys.exit(1)

        # 加载币种特定参数
        self.symbol_params = self.config.get('symbol_specific_params', {})
        if self.symbol_params:
            self.logger.info(f"✅ 已加载 {len(self.symbol_params)} 个币种的专属参数")
        else:
            self.logger.info("⚠️ 未找到币种特定参数，使用全局默认参数")
        
        # 交易参数 (基于回测优化结果)
        self.risk_per_trade = 0.85  # 85%风险
        self.min_signal_score = 1   # 极低信号门槛
        
        # 超短线技术指标参数
        self.ema_ultra_fast = 2
        self.ema_fast = 5
        self.ema_medium = 8
        self.ema_slow = 13
        
        self.macd_fast = 2
        self.macd_slow = 5
        self.macd_signal = 2
        
        self.rsi_period = 3
        self.bb_period = 5
        self.bb_std = 1.5
        
        self.kdj_period = 3
        self.volume_ma = 2
        
        # 止盈止损参数
        self.micro_profit = 0.002   # 0.2%超微利止盈
        self.quick_profit = 0.008   # 0.8%快速止盈
        self.ultra_profit = 0.015   # 1.5%超级止盈
        self.micro_loss = -0.002    # 0.2%超微损止损
        self.max_hold_periods = 10  # 最大持仓30分钟
        
        # 交易状态
        self.positions = {}
        self.trades = []
        self.balance = 1000  # 初始资金
        self.running = False

        # 主要交易币种 (使用配置文件中的币种)
        self.primary_symbols = self.config.get('symbols', ['ETH-USDT-SWAP', 'SOL-USDT-SWAP'])

        # 动态交易频率控制
        self.dynamic_mode = True
        self.hourly_trade_count = 0
        self.last_hour_reset = time.time()
        self.current_max_trades = 20
        self.current_min_hold_time = 0
        self.current_market_state = "正常"

        # 自动重启机制
        self.max_consecutive_errors = 10
        self.consecutive_errors = 0
        self.last_successful_trade = time.time()

        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        self.logger.info("⚡ 3分钟超级高频实盘交易引擎已初始化")
        self.logger.info(f"💰 初始资金: {self.balance} USDT")
        self.logger.info(f"📊 全局风险比例: {self.risk_per_trade*100}%")
        self.logger.info(f"🎯 全局止盈: {self.micro_profit*100}%")
        self.logger.info(f"📉 全局止损: {self.micro_loss*100}%")
        self.logger.info(f"🪙 监控币种: {', '.join(self.primary_symbols)}")

        # 显示币种特定参数
        if self.symbol_params:
            self.logger.info("🎯 币种特定参数已加载:")
            for symbol, params in self.symbol_params.items():
                expected = params.get('expected_performance', {})
                self.logger.info(f"   {symbol}: 止盈{params.get('basic_profit', 0.8)}%/{params.get('medium_profit', 1.5)}%/{params.get('ultra_profit', 3.0)}% | "
                               f"止损-{params.get('stop_loss', 0.6)}% | 风险{params.get('risk_per_trade', 0.85)*100}% | "
                               f"预期{expected.get('return', 0):.1f}%收益")

    def signal_handler(self, signum, frame):
        """信号处理器 - 优雅关闭"""
        self.logger.info(f"🛑 接收到停止信号 ({signum})，正在优雅关闭...")
        self.running = False

    def generate_signature(self, timestamp, method, request_path, body=''):
        """生成OKX API签名"""
        message = timestamp + method + request_path + body
        mac = hmac.new(bytes(self.secret_key, encoding='utf8'), bytes(message, encoding='utf-8'), digestmod='sha256')
        d = mac.digest()
        return base64.b64encode(d).decode('utf-8')

    def get_headers(self, method, request_path, body=''):
        """获取API请求头"""
        import datetime as dt
        timestamp = dt.datetime.now(dt.timezone.utc).isoformat()[:-3] + 'Z'
        signature = self.generate_signature(timestamp, method, request_path, body)

        return {
            'OK-ACCESS-KEY': self.api_key,
            'OK-ACCESS-SIGN': signature,
            'OK-ACCESS-TIMESTAMP': timestamp,
            'OK-ACCESS-PASSPHRASE': self.passphrase,
            'Content-Type': 'application/json'
        }

    def get_account_balance(self):
        """获取账户余额"""
        try:
            request_path = '/api/v5/account/balance'
            headers = self.get_headers('GET', request_path)

            response = requests.get(self.base_url + request_path, headers=headers, timeout=10)
            data = response.json()

            if data['code'] == '0' and data['data']:
                for balance_info in data['data'][0]['details']:
                    if balance_info['ccy'] == 'USDT':
                        available_balance = float(balance_info['availBal'])
                        self.balance = available_balance
                        self.logger.info(f"💰 账户余额更新: {available_balance:.2f} USDT")
                        return available_balance

            self.logger.warning("⚠️ 未找到USDT余额信息")
            return self.balance

        except Exception as e:
            self.logger.error(f"❌ 获取账户余额异常: {e}")
            return self.balance

    def get_symbol_params(self, symbol):
        """获取币种特定参数"""
        if symbol in self.symbol_params:
            return self.symbol_params[symbol]
        else:
            # 返回全局默认参数
            return {
                'basic_profit': self.micro_profit * 100,
                'medium_profit': 1.5,
                'ultra_profit': 3.0,
                'stop_loss': abs(self.micro_loss) * 100,
                'min_signal_score': 3,
                'rsi_neutral': 20,
                'risk_per_trade': self.risk_per_trade,
                'bb_std': 2.0
            }

    def get_real_time_data(self, symbol, limit=50):
        """获取实时3分钟K线数据"""
        try:
            url = "https://www.okx.com/api/v5/market/candles"
            params = {
                'instId': symbol,
                'bar': '3m',
                'limit': limit
            }
            
            response = requests.get(url, params=params, timeout=5)
            data = response.json()
            
            if data['code'] != '0' or not data['data']:
                self.logger.error(f"❌ 获取{symbol}数据失败: {data.get('msg', '未知错误')}")
                return None
            
            df = pd.DataFrame(data['data'], columns=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'volCcy', 'volCcyQuote', 'confirm'])
            df = df.astype({'open': float, 'high': float, 'low': float, 'close': float, 'volume': float})
            # 修复时间戳转换问题 - 安全处理大整数
            try:
                df['timestamp'] = pd.to_datetime(df['timestamp'].astype(float) / 1000, unit='s')
            except:
                # 如果转换失败，使用当前时间序列
                df['timestamp'] = pd.date_range(start=datetime.now(), periods=len(df), freq='3T')
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 获取{symbol}数据异常: {e}")
            return None

    def calculate_live_indicators(self, df):
        """计算实时技术指标"""
        try:
            # EMA指标
            df['ema_ultra_fast'] = df['close'].ewm(span=self.ema_ultra_fast).mean()
            df['ema_fast'] = df['close'].ewm(span=self.ema_fast).mean()
            df['ema_medium'] = df['close'].ewm(span=self.ema_medium).mean()
            df['ema_slow'] = df['close'].ewm(span=self.ema_slow).mean()
            
            # MACD指标
            ema_fast = df['close'].ewm(span=self.macd_fast).mean()
            ema_slow = df['close'].ewm(span=self.macd_slow).mean()
            df['macd'] = ema_fast - ema_slow
            df['macd_signal'] = df['macd'].ewm(span=self.macd_signal).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']
            
            # RSI指标
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.rsi_period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.rsi_period).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # 布林带指标
            df['bb_middle'] = df['close'].rolling(self.bb_period).mean()
            bb_std = df['close'].rolling(self.bb_period).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * self.bb_std)
            df['bb_lower'] = df['bb_middle'] - (bb_std * self.bb_std)
            df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
            
            # KDJ指标
            low_min = df['low'].rolling(window=self.kdj_period).min()
            high_max = df['high'].rolling(window=self.kdj_period).max()
            rsv = (df['close'] - low_min) / (high_max - low_min) * 100
            df['k'] = rsv.ewm(alpha=1/1.5).mean()
            df['d'] = df['k'].ewm(alpha=1/1.5).mean()
            df['j'] = 3 * df['k'] - 2 * df['d']
            
            # 成交量指标
            df['volume_ma'] = df['volume'].rolling(window=self.volume_ma).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma']
            df['volume_explosion'] = df['volume_ratio'] > 3.0
            df['volume_surge'] = df['volume_ratio'] > 1.8
            
            # 价格变化率
            df['price_change_1'] = df['close'].pct_change()
            df['price_change_2'] = df['close'].pct_change(periods=2)
            df['price_change_3'] = df['close'].pct_change(periods=3)
            
            # 动量指标
            df['momentum_1'] = df['close'] / df['close'].shift(1) - 1
            df['momentum_2'] = df['close'] / df['close'].shift(2) - 1
            df['momentum_3'] = df['close'] / df['close'].shift(3) - 1
            
            # 波动率
            df['volatility'] = df['close'].rolling(window=2).std() / df['close'].rolling(window=2).mean()
            
            # 趋势强度
            df['trend_strength'] = abs(df['ema_ultra_fast'] - df['ema_fast']) / df['close']
            
            # 价格加速度
            df['acceleration'] = df['price_change_1'] - df['price_change_1'].shift(1)
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 计算指标异常: {e}")
            return df

    def get_live_signal_score(self, current, prev, symbol=None):
        """获取实时信号评分 (支持币种特定参数)"""
        try:
            buy_score = 0
            sell_score = 0

            # 获取币种特定参数
            if symbol:
                symbol_params = self.get_symbol_params(symbol)
                rsi_neutral = symbol_params.get('rsi_neutral', 20)
                min_signal_score = symbol_params.get('min_signal_score', 3)
            else:
                rsi_neutral = 20
                min_signal_score = 3
            
            # 检查数据完整性
            required_cols = ['ema_ultra_fast', 'ema_fast', 'ema_medium', 'ema_slow', 'macd', 'macd_signal', 
                            'rsi', 'bb_position', 'k', 'd', 'volume_ratio']
            
            for col in required_cols:
                if pd.isna(current[col]) or pd.isna(prev[col]):
                    return 0, 0
            
            # 1. 超超快EMA信号
            if (prev['ema_ultra_fast'] <= prev['ema_fast'] and current['ema_ultra_fast'] > current['ema_fast']):
                if current['ema_fast'] > current['ema_medium'] > current['ema_slow']:
                    buy_score += 15
                elif current['ema_fast'] > current['ema_medium']:
                    buy_score += 10
                else:
                    buy_score += 6
            elif (prev['ema_ultra_fast'] >= prev['ema_fast'] and current['ema_ultra_fast'] < current['ema_fast']):
                if current['ema_fast'] < current['ema_medium'] < current['ema_slow']:
                    sell_score += 15
                elif current['ema_fast'] < current['ema_medium']:
                    sell_score += 10
                else:
                    sell_score += 6
            
            # EMA快线与中线交叉
            if (prev['ema_fast'] <= prev['ema_medium'] and current['ema_fast'] > current['ema_medium']):
                buy_score += 8
            elif (prev['ema_fast'] >= prev['ema_medium'] and current['ema_fast'] < current['ema_medium']):
                sell_score += 8
            
            # EMA排列加分
            if current['ema_ultra_fast'] > current['ema_fast'] > current['ema_medium'] > current['ema_slow']:
                buy_score += 5
            elif current['ema_ultra_fast'] < current['ema_fast'] < current['ema_medium'] < current['ema_slow']:
                sell_score += 5
            
            # 2. MACD信号
            if prev['macd'] <= prev['macd_signal'] and current['macd'] > current['macd_signal']:
                buy_score += 10
            elif prev['macd'] >= prev['macd_signal'] and current['macd'] < current['macd_signal']:
                sell_score += 10
            
            # MACD柱状图动量
            if current['macd_histogram'] > prev['macd_histogram']:
                if current['macd_histogram'] > 0:
                    buy_score += 4
            else:
                if current['macd_histogram'] < 0:
                    sell_score += 4
            
            # 3. 布林带信号
            if current['bb_position'] < 0.1:
                buy_score += 12
            elif current['bb_position'] < 0.2:
                buy_score += 8
            elif current['bb_position'] > 0.9:
                sell_score += 12
            elif current['bb_position'] > 0.8:
                sell_score += 8
            
            # 4. RSI信号 (使用币种特定参数)
            rsi_high = 100 - rsi_neutral
            if prev['rsi'] <= rsi_neutral and current['rsi'] > rsi_neutral:
                buy_score += 10
            elif prev['rsi'] >= rsi_high and current['rsi'] < rsi_high:
                sell_score += 10
            elif current['rsi'] < (rsi_neutral - 5):
                buy_score += 8
            elif current['rsi'] > (rsi_high + 5):
                sell_score += 8
            
            # 5. KDJ信号
            if prev['k'] <= prev['d'] and current['k'] > current['d']:
                if current['k'] < 15:
                    buy_score += 12
                elif current['k'] < 30:
                    buy_score += 8
                else:
                    buy_score += 4
            elif prev['k'] >= prev['d'] and current['k'] < current['d']:
                if current['k'] > 85:
                    sell_score += 12
                elif current['k'] > 70:
                    sell_score += 8
                else:
                    sell_score += 4
            
            # 6. 成交量确认
            if current['volume_explosion']:
                if buy_score > sell_score:
                    buy_score += 8
                elif sell_score > buy_score:
                    sell_score += 8
            elif current['volume_surge']:
                if buy_score > sell_score:
                    buy_score += 4
                elif sell_score > buy_score:
                    sell_score += 4
            
            # 7. 动量信号
            if (current['momentum_1'] > 0.002 and current['momentum_2'] > 0.003 and 
                current['momentum_3'] > 0.004):
                buy_score += 6
            elif (current['momentum_1'] < -0.002 and current['momentum_2'] < -0.003 and 
                  current['momentum_3'] < -0.004):
                sell_score += 6
            
            return buy_score, sell_score
            
        except Exception as e:
            self.logger.error(f"❌ 计算信号评分异常: {e}")
            return 0, 0

    def place_order(self, symbol, side, size, order_type='market'):
        """下单到OKX交易所"""
        try:
            request_path = '/api/v5/trade/order'

            # 构建订单参数
            order_data = {
                'instId': symbol,
                'tdMode': self.config.get('trade_mode', 'cross'),
                'side': side,
                'ordType': order_type,
                'sz': str(size)
            }

            body = json.dumps(order_data)
            headers = self.get_headers('POST', request_path, body)

            response = requests.post(self.base_url + request_path, headers=headers, data=body, timeout=10)
            data = response.json()

            if data['code'] == '0' and data['data']:
                order_id = data['data'][0]['ordId']
                self.logger.info(f"✅ 订单提交成功 {symbol} | {side.upper()} | 数量:{size} | 订单ID:{order_id}")
                return order_id
            else:
                self.logger.error(f"❌ 订单提交失败 {symbol}: {data.get('msg', '未知错误')}")
                return None

        except Exception as e:
            self.logger.error(f"❌ 下单异常 {symbol}: {e}")
            return None

    def get_order_status(self, symbol, order_id):
        """查询订单状态"""
        try:
            request_path = f'/api/v5/trade/order?instId={symbol}&ordId={order_id}'
            headers = self.get_headers('GET', request_path)

            response = requests.get(self.base_url + request_path, headers=headers, timeout=10)
            data = response.json()

            if data['code'] == '0' and data['data']:
                return data['data'][0]
            return None

        except Exception as e:
            self.logger.error(f"❌ 查询订单状态异常: {e}")
            return None

    def execute_live_trade(self, symbol, side, price, score):
        """执行实盘交易 (使用币种特定参数)"""
        try:
            if symbol in self.positions:
                return False

            # 获取币种特定的风险比例
            symbol_params = self.get_symbol_params(symbol)
            risk_ratio = symbol_params.get('risk_per_trade', self.risk_per_trade)

            # 计算仓位大小
            risk_amount = self.balance * risk_ratio
            position_size = risk_amount / price

            # 检查最小交易数量
            min_size = self.config.get('min_trade_size', 0.001)
            if position_size < min_size:
                self.logger.warning(f"⚠️ {symbol} 仓位太小 {position_size:.6f} < {min_size}")
                return False

            # 提交订单
            order_id = self.place_order(symbol, side, position_size)
            if not order_id:
                return False

            # 等待订单成交
            time.sleep(2)
            order_info = self.get_order_status(symbol, order_id)

            if order_info and order_info['state'] == 'filled':
                actual_price = float(order_info['avgPx'])
                actual_size = float(order_info['accFillSz'])

                self.positions[symbol] = {
                    'side': side,
                    'price': actual_price,
                    'size': actual_size,
                    'timestamp': datetime.now(),
                    'score': score,
                    'entry_time': time.time(),
                    'order_id': order_id
                }

                self.logger.info(f"⚡ 实盘开仓成功 {symbol} | {side.upper()} | 价格:{actual_price:.2f} | 数量:{actual_size:.6f} | 评分:{score}")
                self.consecutive_errors = 0  # 重置错误计数
                self.last_successful_trade = time.time()
                return True
            else:
                self.logger.error(f"❌ 订单未成交 {symbol} | 订单ID:{order_id}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 执行交易异常: {e}")
            self.consecutive_errors += 1
            return False

    def check_live_exit(self, symbol, current_price, current_time):
        """检查实盘平仓条件 (使用币种特定参数)"""
        try:
            if symbol not in self.positions:
                return False

            position = self.positions[symbol]

            # 获取币种特定参数
            symbol_params = self.get_symbol_params(symbol)
            basic_profit = symbol_params.get('basic_profit', 0.8) / 100
            medium_profit = symbol_params.get('medium_profit', 1.5) / 100
            ultra_profit = symbol_params.get('ultra_profit', 3.0) / 100
            stop_loss = -symbol_params.get('stop_loss', 0.6) / 100

            # 计算盈亏
            if position['side'] == 'buy':
                pnl_pct = (current_price - position['price']) / position['price']
            else:
                pnl_pct = (position['price'] - current_price) / position['price']
            
            should_close = False
            reason = ""
            
            # 动态止盈止损 (使用币种特定参数)
            score_multiplier = min(position['score'] / 15, 2.0)
            hold_time_minutes = (current_time - position['entry_time']) / 60

            # 使用币种特定的止盈止损参数
            dynamic_basic_profit = basic_profit * score_multiplier
            dynamic_medium_profit = medium_profit * score_multiplier
            dynamic_ultra_profit = ultra_profit * score_multiplier

            # 止盈止损检查 (使用币种特定参数)
            if pnl_pct >= dynamic_basic_profit:
                should_close = True
                reason = f"基础止盈({dynamic_basic_profit*100:.2f}%)-{symbol}"
            elif pnl_pct >= dynamic_medium_profit:
                should_close = True
                reason = f"中级止盈({dynamic_medium_profit*100:.1f}%)-{symbol}"
            elif pnl_pct >= dynamic_ultra_profit:
                should_close = True
                reason = f"超级止盈({dynamic_ultra_profit*100:.1f}%)-{symbol}"
            elif pnl_pct <= stop_loss:
                should_close = True
                reason = f"止损({stop_loss*100}%)-{symbol}"
            # 持仓时间限制已完全移除 - 只依靠技术信号和止盈止损
            
            if should_close:
                return self.close_live_position(symbol, current_price, reason, pnl_pct)
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ 检查平仓条件异常: {e}")
            return False

    def close_live_position(self, symbol, price, reason, pnl_pct):
        """平仓实盘交易"""
        try:
            if symbol not in self.positions:
                return False

            position = self.positions[symbol]

            # 确定平仓方向
            close_side = 'sell' if position['side'] == 'buy' else 'buy'

            # 提交平仓订单
            order_id = self.place_order(symbol, close_side, position['size'])
            if not order_id:
                self.logger.error(f"❌ 平仓订单提交失败 {symbol}")
                return False

            # 等待订单成交
            time.sleep(2)
            order_info = self.get_order_status(symbol, order_id)

            if order_info and order_info['state'] == 'filled':
                actual_exit_price = float(order_info['avgPx'])

                # 重新计算实际盈亏
                if position['side'] == 'buy':
                    actual_pnl_pct = (actual_exit_price - position['price']) / position['price']
                else:
                    actual_pnl_pct = (position['price'] - actual_exit_price) / position['price']

                pnl_amount = position['size'] * position['price'] * actual_pnl_pct

                # 更新余额 (从交易所获取最新余额)
                self.get_account_balance()

                trade = {
                    'symbol': symbol,
                    'side': position['side'],
                    'entry_price': position['price'],
                    'exit_price': actual_exit_price,
                    'entry_time': position['timestamp'],
                    'exit_time': datetime.now(),
                    'pnl_pct': actual_pnl_pct,
                    'pnl_amount': pnl_amount,
                    'reason': reason,
                    'score': position['score'],
                    'balance': self.balance,
                    'close_order_id': order_id
                }

                self.trades.append(trade)
                del self.positions[symbol]

                self.logger.info(f"💥 实盘平仓成功 {symbol} | 价格:{actual_exit_price:.2f} | 盈亏:{actual_pnl_pct*100:.3f}% | 原因:{reason} | 余额:{self.balance:.2f}")

                # 保存交易记录
                self.save_trade_record(trade)
                self.consecutive_errors = 0  # 重置错误计数
                return True
            else:
                self.logger.error(f"❌ 平仓订单未成交 {symbol} | 订单ID:{order_id}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 平仓异常: {e}")
            self.consecutive_errors += 1
            return False

    def save_trade_record(self, trade):
        """保存交易记录"""
        try:
            trade_record = {
                'timestamp': trade['exit_time'].isoformat(),
                'symbol': trade['symbol'],
                'side': trade['side'],
                'entry_price': trade['entry_price'],
                'exit_price': trade['exit_price'],
                'pnl_pct': trade['pnl_pct'],
                'pnl_amount': trade['pnl_amount'],
                'reason': trade['reason'],
                'balance': trade['balance']
            }
            
            with open('3m_trades.json', 'a', encoding='utf-8') as f:
                f.write(json.dumps(trade_record, ensure_ascii=False) + '\n')

        except Exception as e:
            self.logger.error(f"❌ 保存交易记录异常: {e}")

    def check_trading_frequency_limit(self):
        """检查交易频率限制"""
        current_time = time.time()

        # 每小时重置交易计数
        if current_time - self.last_hour_reset > 3600:
            self.hourly_trade_count = 0
            self.last_hour_reset = current_time

        # 检查是否超过每小时最大交易次数
        if self.hourly_trade_count >= self.current_max_trades:
            return False

        return True

    def analyze_market_state(self, df):
        """分析市场状态并动态调整参数"""
        try:
            if len(df) < 10:
                return

            # 计算波动率
            volatility = df['close'].pct_change().std() * 100

            # 计算趋势强度
            ema_diff = abs(df['ema_ultra_fast'].iloc[-1] - df['ema_fast'].iloc[-1]) / df['close'].iloc[-1] * 100

            # 根据市场状态调整参数
            if volatility > 2.0:
                self.current_market_state = "高波动"
                self.current_max_trades = 20
                self.current_min_hold_time = 0
            elif volatility > 1.0:
                self.current_market_state = "中等波动"
                self.current_max_trades = 12
                self.current_min_hold_time = 0
            else:
                self.current_market_state = "低波动"
                self.current_max_trades = 6
                self.current_min_hold_time = 0

        except Exception as e:
            self.logger.error(f"❌ 分析市场状态异常: {e}")

    def check_system_health(self):
        """检查系统健康状态"""
        current_time = time.time()

        # 检查连续错误次数
        if self.consecutive_errors >= self.max_consecutive_errors:
            self.logger.error(f"❌ 连续错误次数过多 ({self.consecutive_errors})，系统需要重启")
            return False

        # 检查是否长时间没有成功交易
        if current_time - self.last_successful_trade > 7200:  # 2小时
            self.logger.warning("⚠️ 长时间没有成功交易，可能存在问题")

        return True

    def run_live_trading(self):
        """运行实盘交易主循环"""
        self.logger.info("🚀 启动3分钟超级高频实盘交易")
        self.running = True

        # 初始化账户余额
        self.get_account_balance()

        last_check_time = {}
        last_balance_update = time.time()
        last_health_check = time.time()

        try:
            while self.running:
                current_time = time.time()

                # 定期检查系统健康状态
                if current_time - last_health_check > 300:  # 每5分钟检查一次
                    if not self.check_system_health():
                        self.logger.error("❌ 系统健康检查失败，停止交易")
                        break
                    last_health_check = current_time

                # 定期更新账户余额
                if current_time - last_balance_update > 600:  # 每10分钟更新一次
                    self.get_account_balance()
                    last_balance_update = current_time

                for symbol in self.primary_symbols:
                    try:
                        # 限制检查频率 (每10秒检查一次)
                        if symbol in last_check_time and current_time - last_check_time[symbol] < 10:
                            continue

                        last_check_time[symbol] = current_time

                        # 获取实时数据
                        df = self.get_real_time_data(symbol, 50)
                        if df is None or len(df) < 20:
                            self.logger.warning(f"⚠️ {symbol} 数据不足，跳过")
                            continue

                        # 计算指标
                        df = self.calculate_live_indicators(df)

                        # 分析市场状态并动态调整参数
                        if self.dynamic_mode:
                            self.analyze_market_state(df)

                        current = df.iloc[-1]
                        prev = df.iloc[-2]
                        current_price = current['close']

                        # 检查平仓条件
                        self.check_live_exit(symbol, current_price, current_time)

                        # 检查开仓条件
                        if symbol not in self.positions:
                            # 检查交易频率限制
                            if not self.check_trading_frequency_limit():
                                continue

                            buy_score, sell_score = self.get_live_signal_score(current, prev, symbol)

                            # 获取币种特定的信号门槛
                            symbol_params = self.get_symbol_params(symbol)
                            min_signal_score = symbol_params.get('min_signal_score', 3)

                            if buy_score >= min_signal_score:
                                if self.execute_live_trade(symbol, 'buy', current_price, buy_score):
                                    self.hourly_trade_count += 1
                                    self.logger.info(f"🎯 {symbol} 买入信号: {buy_score}分≥{min_signal_score}分")
                            elif sell_score >= min_signal_score:
                                if self.execute_live_trade(symbol, 'sell', current_price, sell_score):
                                    self.hourly_trade_count += 1
                                    self.logger.info(f"🎯 {symbol} 卖出信号: {sell_score}分≥{min_signal_score}分")

                    except Exception as e:
                        self.logger.error(f"❌ 处理{symbol}异常: {e}")
                        self.consecutive_errors += 1
                        continue

                # 显示状态
                if len(self.trades) > 0 and len(self.trades) % 5 == 0:
                    self.show_trading_stats()

                # 休眠
                time.sleep(5)  # 每5秒检查一次

        except KeyboardInterrupt:
            self.logger.info("🛑 用户中断交易")
        except Exception as e:
            self.logger.error(f"❌ 交易主循环异常: {e}")
            self.consecutive_errors += 1
        finally:
            self.stop_trading()

    def show_trading_stats(self):
        """显示交易统计"""
        if not self.trades:
            return
        
        total_trades = len(self.trades)
        winning_trades = [t for t in self.trades if t['pnl_pct'] > 0]
        win_rate = len(winning_trades) / total_trades * 100
        total_return = (self.balance - 1000) / 1000 * 100
        
        self.logger.info(f"📊 交易统计 | 总交易:{total_trades} | 胜率:{win_rate:.1f}% | 总收益:{total_return:.2f}% | 余额:{self.balance:.2f}")
        self.logger.info(f"🔄 当前状态 | 市场:{self.current_market_state} | 本小时交易:{self.hourly_trade_count}/{self.current_max_trades} | 持仓时间:{self.current_min_hold_time}分钟")

    def stop_trading(self):
        """停止交易"""
        self.running = False
        
        # 强制平仓所有持仓
        for symbol in list(self.positions.keys()):
            try:
                df = self.get_real_time_data(symbol, 5)
                if df is not None:
                    current_price = df.iloc[-1]['close']
                    position = self.positions[symbol]
                    if position['side'] == 'buy':
                        pnl_pct = (current_price - position['price']) / position['price']
                    else:
                        pnl_pct = (position['price'] - current_price) / position['price']
                    self.close_live_position(symbol, current_price, "强制平仓", pnl_pct)
            except Exception as e:
                self.logger.error(f"❌ 强制平仓{symbol}异常: {e}")
        
        self.show_trading_stats()
        self.logger.info("🏁 3分钟超级高频实盘交易已停止")

def main():
    """主函数"""
    engine = Live3MTradingEngine()
    
    try:
        engine.run_live_trading()
    except Exception as e:
        engine.logger.error(f"❌ 主程序异常: {e}")
    finally:
        engine.stop_trading()

if __name__ == "__main__":
    main()

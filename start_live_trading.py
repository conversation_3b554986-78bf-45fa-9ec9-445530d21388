#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔥 实盘交易启动器
⚠️ 警告：这将进行真实的实盘交易！
"""

import sys
import os
import time
from datetime import datetime

def show_warning():
    """显示实盘交易警告"""
    warning = """
🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥
🔥                                                                    🔥
🔥                        ⚠️ 实盘交易警告 ⚠️                           🔥
🔥                                                                    🔥
🔥  🚨 这是真实的实盘交易系统，会产生真实的盈亏！                        🔥
🔥  💰 请确保您已充分了解交易风险                                      🔥
🔥  📊 建议先用小额资金测试                                           🔥
🔥  🔒 请确认您的API密钥配置正确                                      🔥
🔥  📈 系统将自动执行买卖操作                                         🔥
🔥  🛑 按Ctrl+C可随时停止交易                                        🔥
🔥                                                                    🔥
🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥
    """
    print(warning)

def check_prerequisites():
    """检查前提条件"""
    print("🔍 检查实盘交易前提条件...")
    
    # 检查配置文件
    if not os.path.exists('config.yaml'):
        print("❌ 配置文件 config.yaml 不存在")
        return False
    
    # 检查API配置
    try:
        import yaml
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        api_key = config.get('api_key')
        secret_key = config.get('secret_key')
        passphrase = config.get('passphrase')
        
        if not all([api_key, secret_key, passphrase]):
            print("❌ API配置不完整，请检查config.yaml")
            return False
        
        print("✅ API配置检查通过")
        
    except Exception as e:
        print(f"❌ 配置文件检查失败: {e}")
        return False
    
    # 检查网络连接
    try:
        import requests
        response = requests.get("https://www.okx.com/api/v5/public/time", timeout=10)
        if response.status_code == 200:
            print("✅ OKX服务器连接正常")
        else:
            print("❌ OKX服务器连接异常")
            return False
    except Exception as e:
        print(f"❌ 网络连接检查失败: {e}")
        return False
    
    return True

def get_user_confirmation():
    """获取用户确认"""
    print("\n" + "="*60)
    print("⚠️ 最终确认")
    print("="*60)
    print("您即将启动实盘交易系统，这将：")
    print("• 使用真实资金进行交易")
    print("• 产生真实的盈利或亏损")
    print("• 自动执行买卖决策")
    print("• 收取真实的交易手续费")
    print("")
    
    while True:
        confirm = input("请输入 'START' 确认启动实盘交易，或输入 'CANCEL' 取消: ").strip().upper()
        
        if confirm == 'START':
            print("✅ 用户确认启动实盘交易")
            return True
        elif confirm == 'CANCEL':
            print("❌ 用户取消启动")
            return False
        else:
            print("⚠️ 请输入 'START' 或 'CANCEL'")

def main():
    """主函数"""
    print("🔥 实盘交易系统启动器")
    print(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 显示警告
    show_warning()
    
    # 检查前提条件
    if not check_prerequisites():
        print("\n❌ 前提条件检查失败，无法启动实盘交易")
        input("按回车键退出...")
        return
    
    # 获取用户确认
    if not get_user_confirmation():
        print("\n👋 实盘交易启动已取消")
        input("按回车键退出...")
        return
    
    print("\n🚀 正在启动实盘交易系统...")
    print("⏰ 启动时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    try:
        # 确保当前目录在Python路径中
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from simple_trading_engine import SimpleTradingEngine
        
        print("✅ 实盘交易引擎加载成功")
        
        engine = SimpleTradingEngine()
        print("✅ 实盘交易引擎初始化成功")
        
        print("\n🔥 实盘交易系统已启动！")
        print("💡 按 Ctrl+C 可随时停止交易")
        print("📊 交易日志将保存到 simple_trading.log")
        print("💰 交易记录将保存到 live_trades.json")
        print("=" * 60)
        
        engine.run()
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
    except KeyboardInterrupt:
        print("\n🛑 用户中断实盘交易")
    except Exception as e:
        print(f"❌ 实盘交易启动失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n🏁 实盘交易系统已退出")
        input("按回车键退出...")

if __name__ == "__main__":
    main()

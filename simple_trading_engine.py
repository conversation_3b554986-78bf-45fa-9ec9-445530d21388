#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🤖 简化版3分钟自动交易引擎
修复了所有已知问题的稳定版本
"""

import requests
import pandas as pd
import time
import yaml
import logging
import json
import hmac
import hashlib
import base64
import signal
import sys
from datetime import datetime

class SimpleTradingEngine:
    def __init__(self):
        """初始化交易引擎"""
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('simple_trading.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        with open('config.yaml', 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        # API配置
        self.api_key = self.config.get('api_key')
        self.secret_key = self.config.get('secret_key')
        self.passphrase = self.config.get('passphrase')
        self.base_url = "https://www.okx.com"
        
        # 交易参数
        self.symbols = self.config.get('symbols', ['ETH-USDT-SWAP', 'SOL-USDT-SWAP'])
        self.balance = 1000  # 初始余额
        self.positions = {}
        self.trades = []
        self.running = False

        # 实盘模式标识
        self.live_trading = True  # 设置为True启用实盘交易

        # 技术指标参数
        self.ema_fast = 5
        self.ema_slow = 20
        self.rsi_period = 14

        # 止盈止损
        self.profit_target = 0.008  # 0.8%
        self.stop_loss = -0.006     # -0.6%

        self.logger.info("🔥 实盘交易引擎已初始化")
        self.logger.info(f"⚠️ 实盘模式: {'启用' if self.live_trading else '禁用'}")
        self.logger.info(f"💰 初始余额: {self.balance} USDT")
        self.logger.info(f"🪙 监控币种: {', '.join(self.symbols)}")

        # 获取真实账户余额
        if self.live_trading:
            self.get_account_balance()

    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"🛑 接收到停止信号 ({signum})")
        self.running = False

    def get_market_data(self, symbol, limit=20):
        """获取市场数据"""
        try:
            url = "https://www.okx.com/api/v5/market/candles"
            params = {
                'instId': symbol,
                'bar': '3m',
                'limit': limit
            }
            
            response = requests.get(url, params=params, timeout=15)
            data = response.json()
            
            if data['code'] != '0' or not data['data']:
                self.logger.error(f"❌ 获取{symbol}数据失败")
                return None
            
            # 处理数据
            df = pd.DataFrame(data['data'], columns=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'volCcy', 'volCcyQuote', 'confirm'])
            df = df.astype({'open': float, 'high': float, 'low': float, 'close': float, 'volume': float})
            
            # 安全处理时间戳
            timestamps = []
            for ts in df['timestamp']:
                try:
                    ts_int = int(float(str(ts)))
                    if ts_int > 1e12:  # 毫秒时间戳
                        ts_int = ts_int // 1000
                    timestamps.append(ts_int)
                except:
                    timestamps.append(int(time.time()))
            
            df['timestamp'] = pd.to_datetime(timestamps, unit='s')
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 获取{symbol}数据异常: {e}")
            return None

    def calculate_indicators(self, df):
        """计算技术指标"""
        try:
            # EMA
            df['ema_fast'] = df['close'].ewm(span=self.ema_fast).mean()
            df['ema_slow'] = df['close'].ewm(span=self.ema_slow).mean()
            
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.rsi_period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.rsi_period).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # 成交量
            df['volume_ma'] = df['volume'].rolling(window=5).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma']
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 计算指标异常: {e}")
            return df

    def get_signal(self, df):
        """获取交易信号"""
        try:
            if len(df) < 20:
                return 0, 0
            
            current = df.iloc[-1]
            prev = df.iloc[-2]
            
            buy_score = 0
            sell_score = 0
            
            # EMA金叉死叉
            if prev['ema_fast'] <= prev['ema_slow'] and current['ema_fast'] > current['ema_slow']:
                buy_score += 5
            elif prev['ema_fast'] >= prev['ema_slow'] and current['ema_fast'] < current['ema_slow']:
                sell_score += 5
            
            # RSI超买超卖
            if current['rsi'] < 30:
                buy_score += 3
            elif current['rsi'] > 70:
                sell_score += 3
            
            # 成交量确认
            if current['volume_ratio'] > 1.5:
                if buy_score > sell_score:
                    buy_score += 2
                elif sell_score > buy_score:
                    sell_score += 2
            
            return buy_score, sell_score
            
        except Exception as e:
            self.logger.error(f"❌ 计算信号异常: {e}")
            return 0, 0

    def generate_signature(self, timestamp, method, request_path, body=''):
        """生成OKX API签名"""
        message = timestamp + method + request_path + body
        mac = hmac.new(bytes(self.secret_key, encoding='utf8'), bytes(message, encoding='utf-8'), digestmod='sha256')
        d = mac.digest()
        return base64.b64encode(d).decode('utf-8')

    def get_headers(self, method, request_path, body=''):
        """获取API请求头"""
        import datetime as dt
        timestamp = dt.datetime.now(dt.timezone.utc).isoformat()[:-3] + 'Z'
        signature = self.generate_signature(timestamp, method, request_path, body)

        return {
            'OK-ACCESS-KEY': self.api_key,
            'OK-ACCESS-SIGN': signature,
            'OK-ACCESS-TIMESTAMP': timestamp,
            'OK-ACCESS-PASSPHRASE': self.passphrase,
            'Content-Type': 'application/json'
        }

    def get_account_balance(self):
        """获取真实账户余额"""
        try:
            request_path = '/api/v5/account/balance'
            headers = self.get_headers('GET', request_path)

            response = requests.get(self.base_url + request_path, headers=headers, timeout=15)
            data = response.json()

            if data['code'] == '0' and data['data']:
                for balance_info in data['data'][0]['details']:
                    if balance_info['ccy'] == 'USDT':
                        available_balance = float(balance_info['availBal'])
                        self.balance = available_balance
                        self.logger.info(f"💰 实盘余额: {available_balance:.2f} USDT")
                        return available_balance

                self.logger.warning("⚠️ 未找到USDT余额，使用模拟余额")
                self.balance = 1000
                return self.balance
            else:
                self.logger.error(f"❌ 获取余额失败: {data.get('msg', '未知错误')}")
                return self.balance

        except Exception as e:
            self.logger.error(f"❌ 获取账户余额异常: {e}")
            return self.balance

    def place_order(self, symbol, side, size):
        """下单到OKX交易所"""
        try:
            request_path = '/api/v5/trade/order'

            order_data = {
                'instId': symbol,
                'tdMode': self.config.get('trade_mode', 'cross'),
                'side': side,
                'ordType': 'market',
                'sz': str(size)
            }

            body = json.dumps(order_data)
            headers = self.get_headers('POST', request_path, body)

            response = requests.post(self.base_url + request_path, headers=headers, data=body, timeout=15)
            data = response.json()

            if data['code'] == '0' and data['data']:
                order_id = data['data'][0]['ordId']
                self.logger.info(f"✅ 实盘订单提交成功 {symbol} | {side.upper()} | 数量:{size} | 订单ID:{order_id}")
                return order_id
            else:
                self.logger.error(f"❌ 实盘订单失败 {symbol}: {data.get('msg', '未知错误')}")
                return None

        except Exception as e:
            self.logger.error(f"❌ 实盘下单异常 {symbol}: {e}")
            return None

    def get_order_status(self, symbol, order_id):
        """查询订单状态"""
        try:
            request_path = f'/api/v5/trade/order?instId={symbol}&ordId={order_id}'
            headers = self.get_headers('GET', request_path)

            response = requests.get(self.base_url + request_path, headers=headers, timeout=15)
            data = response.json()

            if data['code'] == '0' and data['data']:
                return data['data'][0]
            return None

        except Exception as e:
            self.logger.error(f"❌ 查询订单状态异常: {e}")
            return None

    def execute_trade(self, symbol, side, price, score):
        """执行实盘交易"""
        try:
            if symbol in self.positions:
                return False

            # 计算仓位大小
            risk_amount = self.balance * 0.1  # 10%风险
            position_size = risk_amount / price

            # 检查最小交易数量
            min_size = self.config.get('min_trade_size', 0.001)
            if position_size < min_size:
                self.logger.warning(f"⚠️ {symbol} 仓位太小 {position_size:.6f} < {min_size}")
                return False

            # 提交实盘订单
            order_id = self.place_order(symbol, side, position_size)
            if not order_id:
                return False

            # 等待订单成交
            time.sleep(3)
            order_info = self.get_order_status(symbol, order_id)

            if order_info and order_info['state'] == 'filled':
                actual_price = float(order_info['avgPx'])
                actual_size = float(order_info['accFillSz'])

                self.positions[symbol] = {
                    'side': side,
                    'price': actual_price,
                    'size': actual_size,
                    'timestamp': datetime.now(),
                    'score': score,
                    'entry_time': time.time(),
                    'order_id': order_id
                }

                self.logger.info(f"⚡ 实盘开仓成功 {symbol} | {side.upper()} | 价格:{actual_price:.2f} | 数量:{actual_size:.6f} | 评分:{score}")
                return True
            else:
                self.logger.error(f"❌ 实盘订单未成交 {symbol} | 订单ID:{order_id}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 实盘交易异常: {e}")
            return False

    def check_exit(self, symbol, current_price):
        """检查平仓条件"""
        try:
            if symbol not in self.positions:
                return False
            
            position = self.positions[symbol]
            
            # 计算盈亏
            if position['side'] == 'buy':
                pnl_pct = (current_price - position['price']) / position['price']
            else:
                pnl_pct = (position['price'] - current_price) / position['price']
            
            should_close = False
            reason = ""
            
            # 止盈止损
            if pnl_pct >= self.profit_target:
                should_close = True
                reason = f"止盈({pnl_pct*100:.2f}%)"
            elif pnl_pct <= self.stop_loss:
                should_close = True
                reason = f"止损({pnl_pct*100:.2f}%)"
            
            if should_close:
                return self.close_position(symbol, current_price, reason, pnl_pct)
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ 检查平仓异常: {e}")
            return False

    def close_position(self, symbol, price, reason, pnl_pct):
        """实盘平仓"""
        try:
            if symbol not in self.positions:
                return False

            position = self.positions[symbol]

            # 确定平仓方向
            close_side = 'sell' if position['side'] == 'buy' else 'buy'

            # 提交实盘平仓订单
            order_id = self.place_order(symbol, close_side, position['size'])
            if not order_id:
                self.logger.error(f"❌ 实盘平仓订单提交失败 {symbol}")
                return False

            # 等待订单成交
            time.sleep(3)
            order_info = self.get_order_status(symbol, order_id)

            if order_info and order_info['state'] == 'filled':
                actual_exit_price = float(order_info['avgPx'])

                # 重新计算实际盈亏
                if position['side'] == 'buy':
                    actual_pnl_pct = (actual_exit_price - position['price']) / position['price']
                else:
                    actual_pnl_pct = (position['price'] - actual_exit_price) / position['price']

                pnl_amount = position['size'] * position['price'] * actual_pnl_pct

                # 更新余额 (重新获取最新余额)
                self.get_account_balance()

                trade = {
                    'symbol': symbol,
                    'side': position['side'],
                    'entry_price': position['price'],
                    'exit_price': actual_exit_price,
                    'entry_time': position['timestamp'],
                    'exit_time': datetime.now(),
                    'pnl_pct': actual_pnl_pct,
                    'pnl_amount': pnl_amount,
                    'reason': reason,
                    'balance': self.balance,
                    'close_order_id': order_id
                }

                self.trades.append(trade)
                del self.positions[symbol]

                self.logger.info(f"💥 实盘平仓成功 {symbol} | 价格:{actual_exit_price:.2f} | 盈亏:{actual_pnl_pct*100:.3f}% | 原因:{reason} | 余额:{self.balance:.2f}")

                # 保存交易记录
                with open('live_trades.json', 'a', encoding='utf-8') as f:
                    f.write(json.dumps(trade, default=str, ensure_ascii=False) + '\n')

                return True
            else:
                self.logger.error(f"❌ 实盘平仓订单未成交 {symbol} | 订单ID:{order_id}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 实盘平仓异常: {e}")
            return False

    def show_stats(self):
        """显示统计"""
        if not self.trades:
            return
        
        total_trades = len(self.trades)
        winning_trades = [t for t in self.trades if t['pnl_pct'] > 0]
        win_rate = len(winning_trades) / total_trades * 100
        total_return = (self.balance - 1000) / 1000 * 100
        
        self.logger.info(f"📊 交易统计 | 总交易:{total_trades} | 胜率:{win_rate:.1f}% | 总收益:{total_return:.2f}% | 余额:{self.balance:.2f}")

    def run(self):
        """运行实盘交易循环"""
        self.logger.info("🔥 启动实盘自动交易系统")
        self.logger.info("⚠️ 警告：这是真实的实盘交易，会产生真实的盈亏！")
        self.running = True

        # 注册信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        last_check_time = {}
        last_balance_update = time.time()

        try:
            while self.running:
                current_time = time.time()

                # 定期更新账户余额 (每5分钟)
                if self.live_trading and current_time - last_balance_update > 300:
                    self.get_account_balance()
                    last_balance_update = current_time

                for symbol in self.symbols:
                    try:
                        # 限制检查频率 (每30秒检查一次)
                        if symbol in last_check_time and current_time - last_check_time[symbol] < 30:
                            continue

                        last_check_time[symbol] = current_time

                        # 获取数据
                        df = self.get_market_data(symbol)
                        if df is None or len(df) < 15:
                            self.logger.warning(f"⚠️ {symbol} 数据不足，跳过")
                            continue

                        # 计算指标
                        df = self.calculate_indicators(df)
                        current_price = df.iloc[-1]['close']

                        # 检查平仓
                        self.check_exit(symbol, current_price)

                        # 检查开仓
                        if symbol not in self.positions:
                            buy_score, sell_score = self.get_signal(df)

                            if buy_score >= 5:
                                self.logger.info(f"🎯 {symbol} 买入信号: {buy_score}分")
                                self.execute_trade(symbol, 'buy', current_price, buy_score)
                            elif sell_score >= 5:
                                self.logger.info(f"🎯 {symbol} 卖出信号: {sell_score}分")
                                self.execute_trade(symbol, 'sell', current_price, sell_score)

                    except Exception as e:
                        self.logger.error(f"❌ 处理{symbol}异常: {e}")
                        continue

                # 显示统计
                if len(self.trades) > 0 and len(self.trades) % 3 == 0:
                    self.show_stats()

                time.sleep(10)  # 每10秒检查一次

        except KeyboardInterrupt:
            self.logger.info("🛑 用户中断交易")
        except Exception as e:
            self.logger.error(f"❌ 交易循环异常: {e}")
        finally:
            self.stop()

    def stop(self):
        """停止交易"""
        self.running = False
        
        # 强制平仓
        for symbol in list(self.positions.keys()):
            try:
                df = self.get_market_data(symbol, 1)
                if df is not None:
                    current_price = df.iloc[-1]['close']
                    position = self.positions[symbol]
                    if position['side'] == 'buy':
                        pnl_pct = (current_price - position['price']) / position['price']
                    else:
                        pnl_pct = (position['price'] - current_price) / position['price']
                    self.close_position(symbol, current_price, "强制平仓", pnl_pct)
            except Exception as e:
                self.logger.error(f"❌ 强制平仓{symbol}异常: {e}")
        
        self.show_stats()
        self.logger.info("🏁 简化版交易已停止")

def main():
    """主函数"""
    engine = SimpleTradingEngine()
    
    try:
        engine.run()
    except Exception as e:
        engine.logger.error(f"❌ 主程序异常: {e}")
    finally:
        engine.stop()

if __name__ == "__main__":
    main()

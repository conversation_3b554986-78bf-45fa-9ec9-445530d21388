#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🤖 简化版3分钟自动交易引擎
修复了所有已知问题的稳定版本
"""

import requests
import pandas as pd
import time
import yaml
import logging
import json
import hmac
import hashlib
import base64
import signal
import sys
from datetime import datetime

class SimpleTradingEngine:
    def __init__(self):
        """初始化交易引擎"""
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('simple_trading.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        with open('config.yaml', 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        # API配置
        self.api_key = self.config.get('api_key')
        self.secret_key = self.config.get('secret_key')
        self.passphrase = self.config.get('passphrase')
        self.base_url = "https://www.okx.com"
        
        # 交易参数
        self.symbols = self.config.get('symbols', ['ETH-USDT-SWAP', 'SOL-USDT-SWAP'])
        self.balance = 1000  # 模拟余额
        self.positions = {}
        self.trades = []
        self.running = False
        
        # 技术指标参数
        self.ema_fast = 5
        self.ema_slow = 20
        self.rsi_period = 14
        
        # 止盈止损
        self.profit_target = 0.008  # 0.8%
        self.stop_loss = -0.006     # -0.6%
        
        self.logger.info("🤖 简化版交易引擎已初始化")
        self.logger.info(f"💰 模拟余额: {self.balance} USDT")
        self.logger.info(f"🪙 监控币种: {', '.join(self.symbols)}")

    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"🛑 接收到停止信号 ({signum})")
        self.running = False

    def get_market_data(self, symbol, limit=20):
        """获取市场数据"""
        try:
            url = "https://www.okx.com/api/v5/market/candles"
            params = {
                'instId': symbol,
                'bar': '3m',
                'limit': limit
            }
            
            response = requests.get(url, params=params, timeout=15)
            data = response.json()
            
            if data['code'] != '0' or not data['data']:
                self.logger.error(f"❌ 获取{symbol}数据失败")
                return None
            
            # 处理数据
            df = pd.DataFrame(data['data'], columns=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'volCcy', 'volCcyQuote', 'confirm'])
            df = df.astype({'open': float, 'high': float, 'low': float, 'close': float, 'volume': float})
            
            # 安全处理时间戳
            timestamps = []
            for ts in df['timestamp']:
                try:
                    ts_int = int(float(str(ts)))
                    if ts_int > 1e12:  # 毫秒时间戳
                        ts_int = ts_int // 1000
                    timestamps.append(ts_int)
                except:
                    timestamps.append(int(time.time()))
            
            df['timestamp'] = pd.to_datetime(timestamps, unit='s')
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 获取{symbol}数据异常: {e}")
            return None

    def calculate_indicators(self, df):
        """计算技术指标"""
        try:
            # EMA
            df['ema_fast'] = df['close'].ewm(span=self.ema_fast).mean()
            df['ema_slow'] = df['close'].ewm(span=self.ema_slow).mean()
            
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.rsi_period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.rsi_period).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # 成交量
            df['volume_ma'] = df['volume'].rolling(window=5).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma']
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 计算指标异常: {e}")
            return df

    def get_signal(self, df):
        """获取交易信号"""
        try:
            if len(df) < 20:
                return 0, 0
            
            current = df.iloc[-1]
            prev = df.iloc[-2]
            
            buy_score = 0
            sell_score = 0
            
            # EMA金叉死叉
            if prev['ema_fast'] <= prev['ema_slow'] and current['ema_fast'] > current['ema_slow']:
                buy_score += 5
            elif prev['ema_fast'] >= prev['ema_slow'] and current['ema_fast'] < current['ema_slow']:
                sell_score += 5
            
            # RSI超买超卖
            if current['rsi'] < 30:
                buy_score += 3
            elif current['rsi'] > 70:
                sell_score += 3
            
            # 成交量确认
            if current['volume_ratio'] > 1.5:
                if buy_score > sell_score:
                    buy_score += 2
                elif sell_score > buy_score:
                    sell_score += 2
            
            return buy_score, sell_score
            
        except Exception as e:
            self.logger.error(f"❌ 计算信号异常: {e}")
            return 0, 0

    def execute_trade(self, symbol, side, price, score):
        """执行交易（模拟）"""
        try:
            if symbol in self.positions:
                return False
            
            # 计算仓位
            risk_amount = self.balance * 0.1  # 10%风险
            position_size = risk_amount / price
            
            self.positions[symbol] = {
                'side': side,
                'price': price,
                'size': position_size,
                'timestamp': datetime.now(),
                'score': score,
                'entry_time': time.time()
            }
            
            self.logger.info(f"⚡ 模拟开仓 {symbol} | {side.upper()} | 价格:{price:.2f} | 评分:{score}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 执行交易异常: {e}")
            return False

    def check_exit(self, symbol, current_price):
        """检查平仓条件"""
        try:
            if symbol not in self.positions:
                return False
            
            position = self.positions[symbol]
            
            # 计算盈亏
            if position['side'] == 'buy':
                pnl_pct = (current_price - position['price']) / position['price']
            else:
                pnl_pct = (position['price'] - current_price) / position['price']
            
            should_close = False
            reason = ""
            
            # 止盈止损
            if pnl_pct >= self.profit_target:
                should_close = True
                reason = f"止盈({pnl_pct*100:.2f}%)"
            elif pnl_pct <= self.stop_loss:
                should_close = True
                reason = f"止损({pnl_pct*100:.2f}%)"
            
            if should_close:
                return self.close_position(symbol, current_price, reason, pnl_pct)
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ 检查平仓异常: {e}")
            return False

    def close_position(self, symbol, price, reason, pnl_pct):
        """平仓"""
        try:
            if symbol not in self.positions:
                return False
            
            position = self.positions[symbol]
            pnl_amount = position['size'] * position['price'] * pnl_pct
            self.balance += pnl_amount
            
            trade = {
                'symbol': symbol,
                'side': position['side'],
                'entry_price': position['price'],
                'exit_price': price,
                'entry_time': position['timestamp'],
                'exit_time': datetime.now(),
                'pnl_pct': pnl_pct,
                'pnl_amount': pnl_amount,
                'reason': reason,
                'balance': self.balance
            }
            
            self.trades.append(trade)
            del self.positions[symbol]
            
            self.logger.info(f"💥 模拟平仓 {symbol} | 价格:{price:.2f} | 盈亏:{pnl_pct*100:.3f}% | 原因:{reason} | 余额:{self.balance:.2f}")
            
            # 保存交易记录
            with open('simple_trades.json', 'a', encoding='utf-8') as f:
                f.write(json.dumps(trade, default=str, ensure_ascii=False) + '\n')
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 平仓异常: {e}")
            return False

    def show_stats(self):
        """显示统计"""
        if not self.trades:
            return
        
        total_trades = len(self.trades)
        winning_trades = [t for t in self.trades if t['pnl_pct'] > 0]
        win_rate = len(winning_trades) / total_trades * 100
        total_return = (self.balance - 1000) / 1000 * 100
        
        self.logger.info(f"📊 交易统计 | 总交易:{total_trades} | 胜率:{win_rate:.1f}% | 总收益:{total_return:.2f}% | 余额:{self.balance:.2f}")

    def run(self):
        """运行交易循环"""
        self.logger.info("🚀 启动简化版自动交易")
        self.running = True
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        last_check_time = {}
        
        try:
            while self.running:
                current_time = time.time()
                
                for symbol in self.symbols:
                    try:
                        # 限制检查频率
                        if symbol in last_check_time and current_time - last_check_time[symbol] < 30:
                            continue
                        
                        last_check_time[symbol] = current_time
                        
                        # 获取数据
                        df = self.get_market_data(symbol)
                        if df is None or len(df) < 15:
                            continue
                        
                        # 计算指标
                        df = self.calculate_indicators(df)
                        current_price = df.iloc[-1]['close']
                        
                        # 检查平仓
                        self.check_exit(symbol, current_price)
                        
                        # 检查开仓
                        if symbol not in self.positions:
                            buy_score, sell_score = self.get_signal(df)
                            
                            if buy_score >= 5:
                                self.execute_trade(symbol, 'buy', current_price, buy_score)
                            elif sell_score >= 5:
                                self.execute_trade(symbol, 'sell', current_price, sell_score)
                        
                    except Exception as e:
                        self.logger.error(f"❌ 处理{symbol}异常: {e}")
                        continue
                
                # 显示统计
                if len(self.trades) > 0 and len(self.trades) % 5 == 0:
                    self.show_stats()
                
                time.sleep(10)  # 每10秒检查一次
                
        except KeyboardInterrupt:
            self.logger.info("🛑 用户中断交易")
        except Exception as e:
            self.logger.error(f"❌ 交易循环异常: {e}")
        finally:
            self.stop()

    def stop(self):
        """停止交易"""
        self.running = False
        
        # 强制平仓
        for symbol in list(self.positions.keys()):
            try:
                df = self.get_market_data(symbol, 1)
                if df is not None:
                    current_price = df.iloc[-1]['close']
                    position = self.positions[symbol]
                    if position['side'] == 'buy':
                        pnl_pct = (current_price - position['price']) / position['price']
                    else:
                        pnl_pct = (position['price'] - current_price) / position['price']
                    self.close_position(symbol, current_price, "强制平仓", pnl_pct)
            except Exception as e:
                self.logger.error(f"❌ 强制平仓{symbol}异常: {e}")
        
        self.show_stats()
        self.logger.info("🏁 简化版交易已停止")

def main():
    """主函数"""
    engine = SimpleTradingEngine()
    
    try:
        engine.run()
    except Exception as e:
        engine.logger.error(f"❌ 主程序异常: {e}")
    finally:
        engine.stop()

if __name__ == "__main__":
    main()

#!/bin/bash

# 全自动3分钟实盘交易系统启动脚本

echo ""
echo "========================================"
echo "    全自动3分钟实盘交易系统"
echo "========================================"
echo ""
echo "🤖 正在启动全自动交易系统..."
echo "⏰ 启动时间: $(date)"
echo "📁 工作目录: $(pwd)"
echo ""

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装"
    echo "请先安装Python 3.8+"
    exit 1
fi

# 检查必要文件
if [ ! -f "config.yaml" ]; then
    echo "❌ 配置文件 config.yaml 不存在"
    exit 1
fi

if [ ! -f "auto_start_trading.py" ]; then
    echo "❌ 启动脚本 auto_start_trading.py 不存在"
    exit 1
fi

# 安装依赖包
echo "📦 检查并安装依赖包..."
pip3 install -r requirements.txt --quiet

# 启动自动交易
echo ""
echo "🚀 启动全自动交易系统..."
echo "💡 按 Ctrl+C 可以停止交易"
echo ""

python3 auto_start_trading.py

echo ""
echo "🏁 交易系统已退出"

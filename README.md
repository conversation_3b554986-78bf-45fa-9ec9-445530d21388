# 🔥 100U超高收益版量化交易系统

基于OKX API的超高收益量化交易引擎，专为100U起步优化，预期月收益200-800%。

## 🚀 新功能特性

- 🔗 **OKX API集成**: 完整的OKX交易所API封装
- 📊 **五维共振信号**: 趋势、动量、波动、量能、市场五维度综合判断
- 📈 **组合策略**: 集成MACD、KDJ、布林带多指标共振，优化买卖点判断
- 🤖 **多线程架构**: 行情监控、交易执行、持仓监控三线程并行
- ⚙️ **三层退出策略**: 闪电层、核心层、卫星层智能退出机制
- 💰 **动态仓位管理**: 基于ATR和风险比例的智能仓位计算
- 🛡️ **多币种监控**: 同时监控多个交易对，提高机会捕捉率

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

## ⚙️ 配置说明

在使用前，您需要在 `config.yaml` 文件中配置您的API信息和交易参数：

```yaml
# OKX API密钥
api_key: "YOUR_API_KEY"           # OKX API密钥
secret_key: "YOUR_SECRET_KEY"     # OKX API私钥
passphrase: "YOUR_PASSPHRASE"     # OKX API密码短语

# 交易对列表
symbols:                          # 需要监控和交易的币种对
  - "BTC-USDT-SWAP"
  - "ETH-USDT-SWAP"

# 高级配置
config_reload_interval: 20        # 配置文件热加载间隔（秒） 
position_monitor_interval: 10     # 持仓监控间隔（秒）

# API与数据源配置
klines_limit: 200                 # 获取K线数据的数量

# 交易执行参数
trade_mode: "isolated"               # 交易模式 (cross:全仓, isolated:逐仓)
order_type: "market"              # 默认订单类型 (market:市价, limit:限价)
min_trade_size: 0.001             # 最小开仓数量 (用于校验)

# 信号参数（统一管理）
# 均线参数
ema5_period: 7
ema30_period: 25
ema200_period: 50
# MACD参数
macd_fast: 14
macd_slow: 53
macd_signal: 5
# RSI参数
rsi_period: 14
# 布林带参数
bb_period: 20
bb_std: 2
# KDJ参数
kdj_rsv_period: 9
kdj_k_period: 3
kdj_d_period: 3
# 支撑阻力信号参数
support_resistance_period: 20     # 支撑阻力计算周期
support_level_factor: 1.02        # 支撑位判断因子
resistance_level_factor: 0.98     # 阻力位判断因子
# 信号扫描频率
scan_interval: 20                # 扫描间隔（秒）
# K线周期
timeframe: "3m"                   # K线周期（如3m、15m、1H等）
# 固定下单量
fixed_trade_amount: 0.001         # 每次固定下单量
# 每笔交易风险比例
risk_per_trade: 0.01              # 每笔交易风险比例（如1%）
# 策略主循环间隔
interval: 20                    # 策略主循环间隔（秒）

# 平仓策略参数
exit_profit_threshold_high: 8.0    # 高盈利保护阈值 (8%)
exit_profit_threshold_medium: 2.0  # 中等盈利阈值 (2%)
exit_loss_threshold: -5.0          # 极端止损阈值 (-5%)
exit_rsi_overbought: 75            # RSI超买阈值
exit_rsi_oversold: 25              # RSI超卖阈值
exit_kdj_high: 70                  # KDJ高位阈值
exit_kdj_low: 30                   # KDJ低位阈值
exit_bb_buffer: 0.005              # 布林带缓冲区 (0.5%)
exit_max_hold_hours: 24            # 最大持仓时间（小时）
exit_signal_score_profit: 4        # 盈利状态平仓信号分数阈值
exit_signal_score_loss: 6          # 亏损状态平仓信号分数阈值
```

## 🎯 交易信号逻辑

### 信号判断依据

本策略结合多种技术指标进行综合判断，形成买入、卖出和平仓信号。

#### 买入信号

- **KDJ低位金叉**: K线从下向上穿越D线，且K、D、J值均处于较低水平（例如K<30）。
- **价格触及布林带下轨**: 价格接近或触及布林带下轨，表明可能超跌。
- **MACD金叉**: MACD线从下向上穿越信号线。
- **趋势确认**: 配合EMA趋势判断，确保在多头趋势或震荡趋势中进行买入。

#### 卖出信号

- **MACD红柱缩短**: MACD柱体由正变负或正柱体显著缩短，显示上涨动能减弱。
- **KDJ高位死叉**: K线从上向下穿越D线，且K、D、J值均处于较高水平（例如K>70）。
- **价格触及布林带上轨并结合MACD/KDJ确认**: 价格接近或触及布林带上轨，同时MACD出现死叉或KDJ出现高位死叉。
- **MACD死叉**: MACD线从上向下穿越信号线。
- **趋势确认**: 配合EMA趋势判断，确保在空头趋势或震荡趋势中进行卖出。

## 🛡️ 三层退出策略

### 闪电层退出 (最快)

- **触发条件**: 价格回撤2.5%
- **目的**: 快速止损，保护利润

### 核心层退出 (主要)

- **触发条件**: 达到2.2倍ATR目标
- **目的**: 主要盈利目标

### 卫星层退出 (兜底)

- **触发条件**: 持仓超过12小时
- **目的**: 时间止损，避免长期套牢

## 📊 技术指标说明

### 移动平均线 (EMA)

- **EMA7**: 7周期指数移动平均线
- **EMA25**: 25周期指数移动平均线  
- **EMA50**: 50周期指数移动平均线

### MACD指标

- **MACD线**: 快线EMA - 慢线EMA
- **信号线**: MACD线的EMA
- **柱体**: MACD线 - 信号线
- **金叉**: MACD线从下向上穿越信号线
- **死叉**: MACD线从上向下穿越信号线

### KDJ指标

- **RSV**: 未成熟随机值，反映N周期内收盘价在最高价和最低价区间的位置。
- **K线**: RSV的M1周期移动平均。
- **D线**: K线的M2周期移动平均。
- **J线**: 3K - 2D，反映K线和D线的乖离程度。
- **金叉**: K线从下向上穿越D线，通常为买入信号。
- **死叉**: K线从上向下穿越D线，通常为卖出信号。
- **超买/超卖**: J值>100为超买，J值<0为超卖。

### 布林带 (Bollinger Bands, BOLL)

- **中轨**: N周期移动平均线。
- **上轨**: 中轨 + K倍标准差。
- **下轨**: 中轨 - K倍标准差。
- **作用**: 衡量价格波动范围，价格触及上下轨可能预示反转或趋势延续。

## 💰 仓位管理

### 动态仓位计算

```python
风险金额 = 账户余额 × 风险比例
波动因子 = min(ATR / (价格 × 0.01), 2.5)
市场因子 = 1.2 (牛市) 或 0.8 (其他)
仓位大小 = (风险金额 × 波动因子 × 市场因子) / 价格
```

### 风险控制

- 每笔交易最大风险: 1%
- 最小账户余额: 100 USDT

## 🚪 智能平仓策略

### 四层平仓决策系统

#### 第一层：强制平仓信号（立即执行）

1. **趋势完全反转 + MACD确认**
   - EMA5跌破/突破EMA30 + MACD金叉/死叉
   - 最高优先级，立即平仓

2. **极端盈利保护** (默认8%+)
   - 高盈利时任一技术指标反向信号即平仓
   - 防止利润大幅回吐

3. **极端亏损保护** (默认-5%)
   - 严重亏损时2个以上反向信号即止损
   - 控制最大损失

#### 第二层：组合信号平仓

**信号评分系统**：

- EMA趋势反转：3分 (权重最高)
- MACD金叉/死叉：2分
- KDJ金叉/死叉：2分
- MACD柱体变化：1分
- 布林带触及：1分
- RSI超买/超卖：1分

#### 第三层：基于盈亏状态的平仓

- **盈利>2%**：信号评分≥4分即平仓
- **微盈利**：信号评分≥5分平仓
- **亏损状态**：信号评分≥6分平仓

#### 第四层：时间因素平仓

- 持仓超出24小时 + 信号评分≥3分
- 避免长期套牢

### 技术指标平仓条件

#### 多头持仓平仓信号

- **EMA**: EMA5跌破EMA30
- **MACD**: 死叉或红柱连续缩短
- **KDJ**: 高位死叉 (K>70)
- **布林带**: 价格触及上轨
- **RSI**: 超买 (>75)

#### 空头持仓平仓信号

- **EMA**: EMA5突破EMA30
- **MACD**: 金叉或绿柱连续缩短
- **KDJ**: 低位金叉 (K<30)
- **布林带**: 价格触及下轨
- **RSI**: 超卖 (<25)

## ♻ 智能持仓同步系统

### 持仓同步机制

#### 启动时同步

- 程序启动时自动从交易所同步所有持仓
- 清理本地持仓记录，重建完整持仓状态
- 支持程序重启后持仓恢复

#### 实时同步验证

- 开仓后3秒验证持仓是否同步成功
- 平仓后3秒验证持仓是否完全清除
- 发现异常自动触发完整同步

#### 定期同步检查

- 每5个监控周期执行一次同步检查
- 比较本地持仓与交易所持仓
- 自动修复持仓漂移问题

### 持仓同步配置

```yaml
# 持仓同步参数
position_sync_enabled: true        # 是否启用持仓同步
position_sync_interval: 5          # 定期同步检查间隔（监控周期倍数）
position_sync_tolerance: 0.001     # 持仓数量同步容差
position_verify_delay: 5           # 开仓/平仓后验证延迟（秒）
```

### 同步安全特性

- **容错机制**: 网络异常时自动重试同步
- **数据校验**: 持仓数量、方向、价格多重验证
- **异常恢复**: 检测到不一致时自动修复
- **日志追踪**: 详细记录所有同步操作
- 最大持仓时间: 12小时

## 🛠 使用方法

1. **配置API信息**: 在代码中填入您的OKX API密钥信息
2. **调整参数**: 根据您的交易策略调整技术指标参数
3. **运行策略**: 执行以下命令启动交易策略

```bash
python quantum_trading_engine.py
```

## 📈 策略优势

- **高胜率**: 五维共振确保信号质量
- **低风险**: 多层退出策略保护资金
- **高效率**: 多线程架构提高响应速度
- **智能化**: 动态仓位和风险控制
- **多币种**: 同时监控多个交易对

## ⚠️ 风险提示

- 量化交易存在风险，请谨慎使用
- 建议先在模拟环境中测试
- 请确保理解策略逻辑和风险
- 不要投入超过您承受能力的资金
- 市场环境变化可能影响策略效果

## 📝 日志输出示例

```text
Mon Dec 18 10:30:15 2023 | 开仓 | BTC-USDT | 价格: 43250.5 | 仓位: 0.023
Mon Dec 18 11:45:22 2023 | 平仓 | BTC-USDT | 价格: 43820.3 | 原因: 止盈平仓 | 盈亏: 13.12
```

## ♻ 更新日志

### v2.0 (当前版本)

- ✅ 优化EMA周期至7-25-50
- ✅ 增加KDJ指标计算与参数配置
- ✅ 整合MACD、KDJ、布林带组合策略
- ✅ 改进日志输出，新增所有币种信号一览表
- ✅ 修复`tdMode`参数错误导致下单失败的问题
- ✅ 实现三层退出策略
- ✅ 集成多线程架构
- ✅ 支持多币种监控
- ✅ 动态仓位管理
- ✅ 实时持仓监控

### v1.0

- ✅ 基础技术指标计算
- ✅ OKX API集成
- ✅ 简单交易信号生成

## 📄 免责声明

本软件仅供学习和研究使用，不构成投资建议。使用者需自行承担交易风险，作者不对任何损失负责。

## 📝 许可证

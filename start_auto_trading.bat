@echo off
chcp 65001 >nul
title 全自动3分钟实盘交易系统

echo.
echo ========================================
echo    全自动3分钟实盘交易系统
echo ========================================
echo.
echo 🤖 正在启动全自动交易系统...
echo ⏰ 启动时间: %date% %time%
echo 📁 工作目录: %cd%
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python 3.8+
    pause
    exit /b 1
)

REM 检查必要文件
if not exist "config.yaml" (
    echo ❌ 配置文件 config.yaml 不存在
    pause
    exit /b 1
)

if not exist "auto_start_trading.py" (
    echo ❌ 启动脚本 auto_start_trading.py 不存在
    pause
    exit /b 1
)

REM 安装依赖包
echo 📦 检查并安装依赖包...
pip install -r requirements.txt --quiet

REM 启动自动交易
echo.
echo 🚀 启动全自动交易系统...
echo 💡 按 Ctrl+C 可以停止交易
echo.

python auto_start_trading.py

echo.
echo 🏁 交易系统已退出
pause
